  <sql splitStatements="false">
            <![CDATA[
              CREATE OR REPLACE FUNCTION sandf.fn_get_plan_overview(
    includes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    config_json JSONB;
    overview_config JSONB;
    benefit_key TEXT;
    benefit_name TEXT;
    result_array JSONB := '[]'::jsonb;
    item_count INTEGER := 0;
    MAX_OVERVIEW_ITEMS INTEGER := 5;
BEGIN
    -- Get configuration from config table
    SELECT json_data INTO config_json
    FROM config.json_storage
    WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
    LIMIT 1;

    -- Extract overView configuration
    IF config_json IS NOT NULL THEN
        overview_config := config_json -> 'overView';
    ELSE
        overview_config := '{}'::jsonb;
    END IF;

    IF includes_param IS NOT NULL THEN
        FOREACH benefit_key IN ARRAY includes_param
        LOOP
            EXIT WHEN item_count >= MAX_OVERVIEW_ITEMS;

            IF overview_config ? benefit_key THEN
                benefit_name := overview_config ->> benefit_key;
                result_array := result_array || jsonb_build_array(benefit_name);
                item_count := item_count + 1;
            END IF;
        END LOOP;
    ELSE
        FOR benefit_key IN SELECT jsonb_object_keys(overview_config)
        LOOP
            EXIT WHEN item_count >= MAX_OVERVIEW_ITEMS;

            benefit_name := overview_config ->> benefit_key;
            result_array := result_array || jsonb_build_array(benefit_name);
            item_count := item_count + 1;
        END LOOP;
    END IF;

    RETURN jsonb_build_object('planOverview', result_array);
END;
$$;
        ]]>
        </sql>