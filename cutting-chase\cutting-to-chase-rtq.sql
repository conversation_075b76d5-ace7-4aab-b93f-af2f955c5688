    <sql splitStatements="false">
            <![CDATA[
        CREATE OR REPLACE FUNCTION sandf.fn_get_cutting_to_the_chase_RTQ(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    benefit_premiums_key TEXT := 'benefitPremiums';
    rtq_name TEXT := 'RTQ';
    quote_record RECORD;
    benefit_premiums JSONB;
    carriers_array JSONB := '[]'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefits_array JSONB := '[]'::jsonb;
    benefit_obj JSONB;
    carrier_name TEXT;
    benefit_map JSONB := '{}'::jsonb;

    -- CuttingToTheChase specific variables for calculations
    total_monthly_premium NUMERIC;
    total_annual_premium NUMERIC;
    annual_premium NUMERIC;
    first_carrier_annual NUMERIC := NULL;
    difference_amount NUMERIC;
    difference_percentage NUMERIC;
    carrier_order INT := 0;

    -- Manual calculation variables (like multi-class version)
    key_name TEXT;
    val JSONB;
    mapped_key_name TEXT;
    should_include_key BOOLEAN;
    coverage_order TEXT[] := ARRAY['single', 'couple', 'family'];
    coverage_type TEXT;
    subval JSONB;
    premium_value NUMERIC;

    -- Config variables for manual calculation
    config_json JSONB;
    benefit_to_premium_map JSONB;

    -- New rankings array to store rank as strings
    rankings_array JSONB := '[]'::jsonb;

    -- For user preference ordering
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order_val INTEGER;
    quote_id_val BIGINT;
    quote_uuid_val UUID;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;
BEGIN
    -- Get configuration from config table (for manual calculation)
    SELECT json_data INTO config_json
    FROM config.json_storage
    WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
    LIMIT 1;

    -- Extract configuration mappings
    IF config_json IS NOT NULL THEN
        benefit_to_premium_map := config_json -> 'benefitToPremium';
    ELSE
        benefit_to_premium_map := '{}'::jsonb;
    END IF;

    RAISE NOTICE 'RTQ Configuration loaded for manual calculation:';
    RAISE NOTICE 'benefit_to_premium_map: %', benefit_to_premium_map;

    -- First pass: Build carrier order map and collect all carriers
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description AS carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = rtq_name
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
         AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
    LOOP
        carrier_name := quote_record.carrier_description;
        quote_id_val := quote_record.quote_id;
        quote_uuid_val := quote_record.quote_uuid;
        -- Use the new function to get carrier order
        carrier_order_val := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_id_val,
            quote_uuid_val,
            999999
        );
        -- Build carrier order map using carrier name as key
        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order_val,
                    'quote_id', quote_id_val,
                    'quote_uuid', quote_uuid_val
                )
            );
        END IF;
    END LOOP;

    -- Second pass: Process quotes to build plan data with proper ordering
    carrier_order := 0;
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description AS carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = rtq_name
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
         AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
        ORDER BY sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            q.quote_id,
            q.quote_uuid,
            999999
        ) ASC, c.description ASC
    LOOP
        carrier_name := quote_record.carrier_description;
        carrier_order := carrier_order + 1;
        -- Append current rank as string to rankings array
        rankings_array := rankings_array || to_jsonb(carrier_order::TEXT);

        -- Capture the first carrier name for display purposes
        IF first_carrier_name IS NULL THEN
            first_carrier_name := carrier_name;
        END IF;

        -- Use "Current" for the first carrier, otherwise use actual name
        IF carrier_name = first_carrier_name THEN
            display_carrier_name := 'Current';
        ELSE
            display_carrier_name := carrier_name;
        END IF;

        -- Add carrier to carriers array if not already present
        IF NOT carriers_array ? display_carrier_name THEN
            carriers_array := carriers_array || jsonb_build_array(display_carrier_name);
        END IF;
        -- Extract benefit premiums from formatted_quote_details
        benefit_premiums := quote_record.formatted_quote_details -> benefit_premiums_key;
        -- Process benefit premiums if found
        IF benefit_premiums IS NOT NULL AND jsonb_typeof(benefit_premiums) = 'object' THEN
            -- Manual calculation like multi-class version (sum all individual premiums)
            total_monthly_premium := 0;
            total_annual_premium := 0;

            RAISE NOTICE 'Starting manual calculation for RTQ carrier %', carrier_name;

            -- Sum all premiums for this carrier (excluding pre-calculated totals)
            FOR key_name, val IN
                SELECT j.k1, j.v1
                FROM jsonb_each(benefit_premiums) AS j(k1, v1)
            LOOP
                RAISE NOTICE 'Processing benefit key: % with value: %', key_name, val::TEXT;
                IF key_name NOT IN ('totalMonthlyPremiums', 'annualPremium') THEN
                    -- Get mapped key name from config
                    IF benefit_to_premium_map ? key_name THEN
                        mapped_key_name := benefit_to_premium_map ->> key_name;
                    ELSE
                        mapped_key_name := key_name;
                    END IF;

                    RAISE NOTICE 'Key: % mapped to: %', key_name, mapped_key_name;

                    -- Include all keys for cutting chase (no filtering like rate-sheet)
                    should_include_key := TRUE;

                    RAISE NOTICE 'Should include key %: %', mapped_key_name, should_include_key;

                    IF should_include_key THEN
                        -- Check if nested structure (single/couple/family)
                        IF val ? 'single' OR val ? 'couple' OR val ? 'family' THEN
                            -- Apply special logic for extended health and dental couple coverage using global function
                            IF (mapped_key_name = 'extendedHealth' OR mapped_key_name = 'dental') AND val ? 'couple' AND val ? 'family' THEN
                                DECLARE
                                    updated_couple_data_ctc_rtq JSONB;
                                BEGIN
                                    updated_couple_data_ctc_rtq := sandf.fn_apply_family_to_couple_coverage(
                                        mapped_key_name,
                                        val -> 'couple',
                                        val -> 'family'
                                    );

                                    -- Update the couple data if it was changed
                                    IF updated_couple_data_ctc_rtq != val -> 'couple' THEN
                                        val := jsonb_set(val, ARRAY['couple'], updated_couple_data_ctc_rtq);
                                        RAISE NOTICE 'Cutting-to-Chase RTQ: Applied family values to couple coverage for %', mapped_key_name;
                                    END IF;
                                END;
                            END IF;

                            RAISE NOTICE 'Processing nested structure for key: %', mapped_key_name;
                            FOREACH coverage_type IN ARRAY coverage_order
                            LOOP
                                IF val ? coverage_type THEN
                                    subval := val -> coverage_type;
                                    RAISE NOTICE 'Coverage Type: %, Subval: %', coverage_type, subval::TEXT;
                                    IF subval ? 'premium' THEN
                                        RAISE NOTICE 'Raw premium value from data: %', subval ->> 'premium';
                                        premium_value := sandf.safe_parse_numeric(subval ->> 'premium');
                                        RAISE NOTICE 'Parsed Premium Value for %: %', coverage_type, premium_value;
                                        total_monthly_premium := total_monthly_premium + premium_value;
                                        total_annual_premium := total_annual_premium + (premium_value * 12);
                                        RAISE NOTICE 'Running Total - Monthly: %, Annual: %', total_monthly_premium, total_annual_premium;
                                    END IF;
                                END IF;
                            END LOOP;
                        ELSE
                            -- Direct premium value
                            RAISE NOTICE 'Processing direct premium value for key: %', mapped_key_name;
                            IF val ? 'premium' THEN
                                RAISE NOTICE 'Raw premium value from data (direct): %', val ->> 'premium';
                                premium_value := sandf.safe_parse_numeric(val ->> 'premium');
                                RAISE NOTICE 'Raw Premium: %, Parsed Premium Value: %', val ->> 'premium', premium_value;
                                total_monthly_premium := total_monthly_premium + premium_value;
                                total_annual_premium := total_annual_premium + (premium_value * 12);
                                RAISE NOTICE 'Running Total - Monthly: %, Annual: %', total_monthly_premium, total_annual_premium;
                            END IF;
                        END IF;
                    END IF;
                END IF;
            END LOOP;

            RAISE NOTICE 'Finished manual calculation for RTQ carrier %: monthly total = %, annual total = %', carrier_name, total_monthly_premium, total_annual_premium;

            -- Use the calculated annual premium
            annual_premium := total_annual_premium;
            -- Set first carrier annual for difference calculations
            IF first_carrier_annual IS NULL THEN
                first_carrier_annual := annual_premium;
            END IF;
            -- Calculate differences
            difference_amount := annual_premium - first_carrier_annual;
            IF first_carrier_annual != 0 THEN
                difference_percentage := (difference_amount / first_carrier_annual) * 100;
            ELSE
                difference_percentage := 0;
            END IF;
            -- Add totalMonthlyPremiums benefit (always include for CuttingToTheChase)
            IF NOT benefit_map ? 'calculations.totalMonthlyPremiums' THEN
                benefit_map := benefit_map || jsonb_build_object(
                    'calculations.totalMonthlyPremiums',
                    jsonb_build_object(
                        'name', 'Total Monthly Premiums',
                        'key', 'totalMonthlyPremiums',
                        'section', 'calculations',
                        'values', '{}'::jsonb
                    )
                );
            END IF;
            benefit_map := jsonb_set(
                benefit_map,
                ARRAY['calculations.totalMonthlyPremiums', 'values', display_carrier_name],
                to_jsonb(sandf.fn_format_currency_with_symbol_java_style(total_monthly_premium))
            );
            -- Add differencePercentage benefit (always include for CuttingToTheChase)
            IF carrier_order > 1 THEN
                IF NOT benefit_map ? 'calculations.differencePercentage' THEN
                    benefit_map := benefit_map || jsonb_build_object(
                        'calculations.differencePercentage',
                        jsonb_build_object(
                            'name', 'Percentage Different From #1',
                            'key', 'differencePercentage',
                            'section', 'calculations',
                            'values', '{}'::jsonb
                        )
                    );
                END IF;
                benefit_map := jsonb_set(
                    benefit_map,
                    ARRAY['calculations.differencePercentage', 'values', display_carrier_name],
                    to_jsonb(sandf.fn_format_percentage_java_style(difference_percentage))
                );
            ELSE
                -- For first carrier, set empty value "-" for difference field
                IF NOT benefit_map ? 'calculations.differencePercentage' THEN
                    benefit_map := benefit_map || jsonb_build_object(
                        'calculations.differencePercentage',
                        jsonb_build_object(
                            'name', 'Percentage Different From #1',
                            'key', 'differencePercentage',
                            'section', 'calculations',
                            'values', '{}'::jsonb
                        )
                    );
                END IF;
                benefit_map := jsonb_set(
                    benefit_map,
                    ARRAY['calculations.differencePercentage', 'values', display_carrier_name],
                    to_jsonb('-'::text)
                );
            END IF;
        END IF;
    END LOOP;

    -- Log RTQ totals summary
    RAISE NOTICE '=== RTQ CUTTING CHASE TOTALS SUMMARY ===';
    carrier_order := 0;
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description AS carrier_description
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = rtq_name
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
         AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
        ORDER BY sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            q.quote_id,
            q.quote_uuid,
            999999
        ) ASC, c.description ASC
    LOOP
        carrier_order := carrier_order + 1;
        carrier_name := quote_record.carrier_description;

        -- Use "Current" for first carrier, otherwise use actual name
        IF carrier_order = 1 THEN
            display_carrier_name := 'Current';
        ELSE
            display_carrier_name := carrier_name;
        END IF;

        -- Get the total from benefit_map
        IF benefit_map ? 'calculations.totalMonthlyPremiums' AND
           (benefit_map -> 'calculations.totalMonthlyPremiums' -> 'values') ? display_carrier_name THEN
            RAISE NOTICE 'CARRIER: % (displayed as: %)', carrier_name, display_carrier_name;
            RAISE NOTICE '  Total Monthly Premium: %',
                benefit_map -> 'calculations.totalMonthlyPremiums' -> 'values' ->> display_carrier_name;
            RAISE NOTICE '---';
        END IF;
    END LOOP;
    RAISE NOTICE '=== END OF RTQ TOTALS ===';
    RAISE NOTICE '';

    -- Build benefits array from benefit_map (only totalMonthlyPremiums and differencePercentage)
    IF benefit_map ? 'calculations.totalMonthlyPremiums' THEN
        benefit_obj := benefit_map -> 'calculations.totalMonthlyPremiums';
        benefits_array := benefits_array || jsonb_build_array(benefit_obj);
    END IF;
    IF benefit_map ? 'calculations.differencePercentage' THEN
        benefit_obj := benefit_map -> 'calculations.differencePercentage';
        benefits_array := benefits_array || jsonb_build_array(benefit_obj);
    END IF;
    -- Only create the "Totals" section if we have benefits
    IF jsonb_array_length(benefits_array) > 0 THEN
        section_obj := jsonb_build_object(
            'name', 'Totals',
            'id', 'calculations',
            'benefits', benefits_array
        );
        sections_array := sections_array || jsonb_build_array(section_obj);
    END IF;
    -- Return the results in the new format including rankings
    RETURN jsonb_build_object(
        'carriers', carriers_array,
        'sections', sections_array,
        'rankings', rankings_array
    );
END;
$$;

        ]]>
        </sql>