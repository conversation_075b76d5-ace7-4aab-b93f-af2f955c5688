{
    "planDetails": {
        "lifeInsurance": {
            "lifeInsurance": "Life Insurance",
            "displayItems": {
                "maximumLife": "Maximum LI",
                "ageReduction": "Age Reduction",
                "coverageLife": "Coverage LI",
                "terminationAgeLife": "Termination Age LI"
            }
        },
        "ADAD": {
            "ADAD": "Accidental Death & Dismembermenet",
            "displayItems": {
                "maximumADAD": "Maximum AD&D",
                "terminationAgeADAD": "Termination Age ADAD"
            }
        },
        "dependentLife": {
            "dependentLife": "Dependent Life",
            "displayItems": {
                "child": "Child",
                "spouse": "Spouse"
            }
        },
        "longTermDisability": {
            "longTermDisability": "Long Term Disability",
            "displayItems": {
                "coverageLTD": "Coverage LTD",
                "eliminationPeriod": "Elimination Period LTD",
                "benefitPeriod": "Benefit Period LTD",
                "benefitMaximumLTD": "Benefit Maximum LTD",
                "nonEvidenceMaximum": "Non Evidence Maximum",
                "definitionOfDisability": "Definition of Disability",
                "terminationAgeLTD": "Termination Age LTD",
                "quoteToNEMOrMax": "Quote to NEM or Max"
            }
        },
        "shortTermDisability": {
            "elimination": ""
        },
        "criticalIllness": {
            "criticalIllness": "Critical Illness",
            "displayItems": {
                "amountCI": "Amount",
                "coverageCI": "Coverage",
                "terminationAgeCI": "Termination Age"
            }
        },
        "employeeAssistance": {
            "employeeAssistance": "Employee Assistance",
            "displayItems": {
                "coverageEA": "Coverage"
            }
        },
        "extendedHealth": {
            "extendedHealth": "Extended Health",
            "displayItems": {
                "annualDeductibleEHC": "Annual Deductible",
                "CoInsurance": "Co-Insurance EHC",
                "prescriptionDrugCoInsurance": "Prescription Drug Co-Insurance", // prescriptionDrugs
                "prescriptionMaximum": "Prescription Maximum", // prescriptionDrugs
                "prescriptionDrugType": "Prescription Drug Type", // prescriptionDrugs
                "prescriptionPayDirectDrugCard": "Pay Direct Drug Card", // prescriptionDrugs
                "paramedicalMaximum": "Paramedical Maximum", // prescriptionDrugs
                "visionCare": "Vision",
                "eyeExams": "Eye Exams",
                "privateDutyNursingMaximum": "Private Duty Nursing Maximum",
                "semiPrivateHospital": "Semi Private Hospital",
                "hearingAids": "Hearing Aids",
                "outOfCountryTravel": "Out of Country Emergency",
                "terminationAgeEHC": "Termination Age"
            }
        },
        "dental": {
            "dental": "Dental",
            "displayItems": {
                "annualDeductibleDental": "Annual Deductible",
                "basicCoInsurance": "Basic Co-Insurance",
                "majorCoInsurance": "Major Co-Insurance",
                "orthodonticCoInsurance": "Orthodontic Co-Insurance",
                "basicAndMajorDentalMaximum": "Basic & Major Dental Maximum",
                "orthodonticLifetimeMaximum": "Orthodontic Lifetime Maximum",
                "dentalRecall": "Recall",
                "terminationAgeDental": "Termination Age"
            }
        }
    }
}