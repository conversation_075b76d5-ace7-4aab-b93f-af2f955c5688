
<sql splitStatements="false">
    <![CDATA[
                CREATE OR REPLACE FUNCTION sandf.fn_get_cutting_to_the_chase_global(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    has_rtq_only BOOLEAN := FALSE;
    result JSONB;
BEGIN
    has_rtq_only := sandf.fn_check_employee_class_type(plan_uuid_param);

    IF has_rtq_only THEN
        SELECT sandf.fn_get_cutting_to_the_chase_RTQ(
            plan_uuid_param,
            user_id_param,
            includes_quotes_uuid
        ) INTO result;

    ELSE
        SELECT sandf.fn_get_cutting_chase_multi_class(
            plan_uuid_param,
            user_id_param,
            includes_quotes_uuid
        ) INTO result;

    END IF;

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in global plan design function: % (SQLSTATE: %)', S<PERSON>ERRM, SQLSTATE;

END;
$$;
]]>
        </sql>
