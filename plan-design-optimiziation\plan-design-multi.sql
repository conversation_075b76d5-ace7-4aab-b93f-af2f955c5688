
        <sql splitStatements="false">
            <![CDATA[
            CREATE OR REPLACE FUNCTION sandf.fn_get_plan_design_report_group_class(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$

DECLARE
    plan_details_key TEXT := 'planDetails';
    quote_record RECORD;
    plan_details JSONB;
    fields JSONB;
    field_item JSONB;
    carriers_array JSONB := '[]'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefits_array JSONB;
    benefit_obj JSONB;
    carrier_name TEXT;
    group_name TEXT;
    group_display_name TEXT;
    benefit_name TEXT;
    benefit_key TEXT;
    benefit_values JSONB := '{}'::jsonb;
    field_details JSONB;
    field_detail JSONB;
    carrier_value TEXT;
    section_map JSONB := '{}'::jsonb;
    section_original_names JSONB := '{}'::jsonb;
    section_names TEXT[];
    section_name TEXT;
    section_id TEXT;
    section_display_name TEXT;
    benefit_map JSONB := '{}'::jsonb;
    benefit_keys TEXT[];
    benefit_key_item TEXT;
    section_order_record RECORD;
    benefit_order_record RECORD;
    skip_fields TEXT[] := ARRAY['maximumLife', 'maximumADAD'];
    coverage_life_values JSONB := '{}'::jsonb;
    field_values_to_check JSONB := '{}'::jsonb;
    should_skip_field BOOLEAN;
    carrier_check TEXT;
    coverage_value TEXT;
    field_value TEXT;
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order INTEGER;
    quote_uuid_val UUID;
    MAX_BENEFITS_PER_PAGE INTEGER := 10;
    all_benefits JSONB := '[]'::jsonb;
    all_sections JSONB := '[]'::jsonb;
    current_page_benefits INTEGER := 0;
    current_page_sections JSONB := '[]'::jsonb;
    current_section_benefits JSONB := '[]'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    total_benefits INTEGER;
    current_section_name TEXT;
    current_section_id TEXT;
    current_section_display_name TEXT;
    section_idx INTEGER;
    benefit_idx INTEGER;
    page_object JSONB;
    employee_classes TEXT[];
    employee_class_count INTEGER;
    current_employee_class TEXT;
    class_suffix TEXT;
    class_index INTEGER;
    class_suffix_extracted TEXT;

    -- Class grouping and legend variables (from rate-sheet-v2.sql)
    class_to_letter_map JSONB := '{}'::jsonb;
    class_legend_array TEXT[] := ARRAY[]::TEXT[];
    letter_index INTEGER := 1;
    current_letter TEXT;

    -- Class sections mapping for groupby logic
    class_sections_map JSONB := '{}'::jsonb;
    combined_sections_map JSONB := '{}'::jsonb;
    section_signature TEXT;
    matching_classes TEXT[];
    combined_class_name TEXT;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;
    temp_class_sections_map JSONB;
    temp_coverage_life_values JSONB;
    temp_field_values_to_check JSONB;

    -- Configuration variables for filtering and ordering
    config_json JSONB;
    config_plan_details JSONB;
    config_overview JSONB;
    config_section_key TEXT;
    config_section_info JSONB;
    config_display_items JSONB;
    mapped_section_key TEXT;
    carrier_has_data BOOLEAN;
    config_section_keys TEXT[];
    config_section_order INTEGER;
BEGIN
    -- Load configuration from database using helper function
    config_json := sandf.load_plan_design_config();
    config_plan_details := sandf.get_plan_details_config(config_json);

    -- Get the order of sections from config_plan_details keys using helper function
    config_section_keys := sandf.get_ordered_section_keys(config_plan_details);

    -- Get employee classes using helper function
    employee_classes := sandf.get_employee_classes(plan_uuid_param);
    employee_class_count := array_length(employee_classes, 1);

    -- Build class-to-letter mapping using helper function
    SELECT * INTO class_to_letter_map, class_legend_array
    FROM sandf.build_class_letter_mapping(employee_classes);
    -- Build carrier order map for all employee classes (using first class as reference)
    IF array_length(employee_classes, 1) > 0 THEN
        carrier_order_map := sandf.build_carrier_order_map(plan_uuid_param, user_id_param, employee_classes[1]);
        first_carrier_name := sandf.get_first_carrier_name(carrier_order_map);
    END IF;

    -- Step 1: Collect section data by class for groupby logic using helper function
    class_index := 1;
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        class_suffix := current_employee_class;
        current_letter := class_to_letter_map ->> current_employee_class;

        -- Process class section data using helper function
        SELECT * INTO temp_class_sections_map, temp_coverage_life_values, temp_field_values_to_check
        FROM sandf.process_class_section_data(
            plan_uuid_param,
            current_employee_class,
            carrier_order_map,
            config_plan_details,
            includes_param,
            excludes_param,
            first_carrier_name,
            skip_fields
        );

        -- Merge results into main variables
        class_sections_map := class_sections_map || temp_class_sections_map;
        coverage_life_values := coverage_life_values || temp_coverage_life_values;
        field_values_to_check := field_values_to_check || temp_field_values_to_check;

        -- Complex processing logic is now handled by helper function above

        class_index := class_index + 1;
    END LOOP;

    -- Step 2: Normalize class_sections_map to ensure all benefits have values for all carriers
    DECLARE
        all_carriers TEXT[];
        class_name TEXT;
        section_key TEXT;
        benefit_key_norm TEXT;
        carrier_name_norm TEXT;
        section_data JSONB;
        benefit_data JSONB;
    BEGIN
        -- Get all unique carrier names
        SELECT array_agg(DISTINCT key ORDER BY key) INTO all_carriers
        FROM jsonb_each(carrier_order_map);

        -- For each class and section, ensure all benefits have all carriers
        FOR class_name IN SELECT jsonb_object_keys(class_sections_map)
        LOOP
            FOR section_key IN SELECT jsonb_object_keys(class_sections_map -> class_name)
            LOOP
                section_data := class_sections_map -> class_name -> section_key;
                FOR benefit_key_norm IN SELECT jsonb_object_keys(section_data -> 'values')
                LOOP
                    benefit_data := section_data -> 'values' -> benefit_key_norm;

                    -- Add missing carriers with "-" value
                    FOREACH carrier_name_norm IN ARRAY all_carriers
                    LOOP
                        IF NOT (benefit_data -> 'values') ? carrier_name_norm THEN
                            class_sections_map := jsonb_set(
                                class_sections_map,
                                ARRAY[class_name, section_key, 'values', benefit_key_norm, 'values', carrier_name_norm],
                                to_jsonb('-'::text)
                            );
                        END IF;
                    END LOOP;
                END LOOP;
            END LOOP;
        END LOOP;
    END;

    -- Step 3: Build ordered carriers array using helper function
    ordered_carriers_array := sandf.build_ordered_carriers_array(carrier_order_map, first_carrier_name);

    -- Step 4: Build grouped sections result using helper function
    all_sections := sandf.build_grouped_sections_result(
        class_sections_map,
        employee_classes,
        class_to_letter_map,
        config_plan_details,
        skip_fields,
        coverage_life_values,
        field_values_to_check
    );


    -- Step 5: Pagination logic using helper functions
    total_benefits := sandf.count_total_benefits(all_sections);

    IF total_benefits <= MAX_BENEFITS_PER_PAGE THEN
        RETURN sandf.build_single_page_result(ordered_carriers_array, all_sections);
    END IF;

    current_page_benefits := 0;
    current_page_sections := '[]'::jsonb;
    current_section_benefits := '[]'::jsonb;
    current_section_name := '';

    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        section_obj := all_sections -> section_idx;
        section_name := section_obj ->> 'name';
        section_id := section_obj ->> 'id';
        benefits_array := section_obj -> 'benefits';

        -- Check if adding this entire section would exceed page limit
        -- If so, create new page before adding this section (never split sections)
        IF current_page_benefits > 0 AND
           (current_page_benefits + jsonb_array_length(benefits_array)) > MAX_BENEFITS_PER_PAGE THEN

            -- Finalize current section if we have one
            IF current_section_name != '' AND jsonb_array_length(current_section_benefits) > 0 THEN
                current_page_sections := current_page_sections || jsonb_build_array(
                    jsonb_build_object(
                        'name', current_section_display_name,
                        'id', current_section_id,
                        'benefits', current_section_benefits
                    )
                );
            END IF;

            -- Build page object - include carriers on every page
            page_object := jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', current_page_sections
            );

            result_pages := result_pages || jsonb_build_array(page_object);

            -- Reset for new page
            current_page_sections := '[]'::jsonb;
            current_section_benefits := '[]'::jsonb;
            current_page_benefits := 0;
            current_section_name := '';
        END IF;

        -- Process all benefits in this section (keep section together)
        FOR benefit_idx IN 0..jsonb_array_length(benefits_array)-1 LOOP
            benefit_obj := benefits_array -> benefit_idx;

            IF current_section_name != section_name THEN
                IF current_section_name != '' AND jsonb_array_length(current_section_benefits) > 0 THEN
                    current_page_sections := current_page_sections || jsonb_build_array(
                        jsonb_build_object(
                            'name', current_section_display_name,
                            'id', current_section_id,
                            'benefits', current_section_benefits
                        )
                    );
                END IF;

                current_section_name := section_name;
                current_section_id := section_id;
                current_section_display_name := section_name;
                current_section_benefits := '[]'::jsonb;
            END IF;

            current_section_benefits := current_section_benefits || jsonb_build_array(benefit_obj);
            current_page_benefits := current_page_benefits + 1;
        END LOOP;
    END LOOP;

    -- Handle final section and page
    IF current_section_name != '' AND jsonb_array_length(current_section_benefits) > 0 THEN
        current_page_sections := current_page_sections || jsonb_build_array(
            jsonb_build_object(
                'name', current_section_display_name,
                'id', current_section_id,
                'benefits', current_section_benefits
            )
        );
    END IF;

    IF jsonb_array_length(current_page_sections) > 0 THEN
        -- Build final page object - include carriers on every page
        page_object := jsonb_build_object(
            'carriers', ordered_carriers_array,
            'sections', current_page_sections
        );

        result_pages := result_pages || jsonb_build_array(page_object);
    END IF;

    RETURN result_pages;

END;
$$;
]]>
        </sql>