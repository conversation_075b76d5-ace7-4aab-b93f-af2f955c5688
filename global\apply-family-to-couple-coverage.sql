<sql splitStatements="false">
    <![CDATA[
CREATE OR REPLACE FUNCTION sandf.fn_apply_family_to_couple_coverage(
    benefit_key TEXT,
    couple_data JSONB,
    family_data JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    couple_volume TEXT;
    couple_rate TEXT;
    couple_premium TEXT;
    couple_volume_numeric NUMERIC;
    couple_rate_numeric NUMERIC;
    couple_premium_numeric NUMERIC;
    should_apply_family_values BOOLEAN := FALSE;
    result_data JSONB;
BEGIN
    RAISE NOTICE '=== fn_apply_family_to_couple_coverage START ===';
    RAISE NOTICE 'fn_apply_family_to_couple_coverage: Processing % benefit', benefit_key;
    RAISE NOTICE 'fn_apply_family_to_couple_coverage: Input couple_data: %', couple_data::TEXT;
    RAISE NOTICE 'fn_apply_family_to_couple_coverage: Input family_data: %', family_data::TEXT;

    -- Return original couple data if family data is null or empty
    IF family_data IS NULL OR family_data = '{}'::jsonb THEN
        RAISE NOTICE 'fn_apply_family_to_couple_coverage: Family data is null/empty, returning original couple data';
        RETURN couple_data;
    END IF;

    -- Return original couple data if couple data is null or empty
    IF couple_data IS NULL OR couple_data = '{}'::jsonb THEN
        RAISE NOTICE 'fn_apply_family_to_couple_coverage: Couple data is null/empty, returning original couple data';
        RETURN couple_data;
    END IF;

    -- Extract couple values
    couple_volume := couple_data ->> 'volume';
    couple_rate := couple_data ->> 'rate';
    couple_premium := couple_data ->> 'premium';

    RAISE NOTICE 'fn_apply_family_to_couple_coverage: Extracted couple values - volume: "%", rate: "%", premium: "%"',
        COALESCE(couple_volume, 'NULL'), COALESCE(couple_rate, 'NULL'), COALESCE(couple_premium, 'NULL');

    -- Convert to numeric values for checking
    couple_volume_numeric := sandf.safe_parse_numeric(COALESCE(couple_volume, '0'));
    couple_rate_numeric := sandf.safe_parse_numeric(COALESCE(couple_rate, '0'));
    couple_premium_numeric := sandf.safe_parse_numeric(COALESCE(couple_premium, '0'));

    RAISE NOTICE 'fn_apply_family_to_couple_coverage: Couple numeric values - volume: %, rate: %, premium: %',
        couple_volume_numeric, couple_rate_numeric, couple_premium_numeric;

    -- Check each condition separately for better debugging
    RAISE NOTICE 'fn_apply_family_to_couple_coverage: Checking conditions:';
    RAISE NOTICE '  - Volume condition: % (volume_numeric = 0: %, volume IS NULL: %, TRIM(volume) = '''': %)',
        (couple_volume_numeric = 0 OR couple_volume IS NULL OR TRIM(couple_volume) = ''),
        couple_volume_numeric = 0, couple_volume IS NULL,
        CASE WHEN couple_volume IS NOT NULL THEN TRIM(couple_volume) = '' ELSE FALSE END;
    RAISE NOTICE '  - Rate condition: % (rate_numeric = 0: %, rate IS NULL: %, TRIM(rate) = '''': %)',
        (couple_rate_numeric = 0 OR couple_rate IS NULL OR TRIM(couple_rate) = ''),
        couple_rate_numeric = 0, couple_rate IS NULL,
        CASE WHEN couple_rate IS NOT NULL THEN TRIM(couple_rate) = '' ELSE FALSE END;
    RAISE NOTICE '  - Premium condition: % (premium_numeric = 0: %, premium IS NULL: %, TRIM(premium) = '''': %)',
        (couple_premium_numeric = 0 OR couple_premium IS NULL OR TRIM(couple_premium) = ''),
        couple_premium_numeric = 0, couple_premium IS NULL,
        CASE WHEN couple_premium IS NOT NULL THEN TRIM(couple_premium) = '' ELSE FALSE END;

    -- Check if ALL couple values are empty/0 (volume AND rate AND premium must all be 0/empty)
    IF (couple_volume_numeric = 0 OR couple_volume IS NULL OR TRIM(couple_volume) = '') AND
       (couple_rate_numeric = 0 OR couple_rate IS NULL OR TRIM(couple_rate) = '') AND
       (couple_premium_numeric = 0 OR couple_premium IS NULL OR TRIM(couple_premium) = '') THEN
        should_apply_family_values := TRUE;
        RAISE NOTICE 'fn_apply_family_to_couple_coverage: ✓ ALL couple values (volume, rate, premium) are empty/0, will apply family values';
    ELSE
        RAISE NOTICE 'fn_apply_family_to_couple_coverage: ✗ At least one couple value is present, keeping original couple data';
    END IF;

    -- Apply family values if needed
    IF should_apply_family_values THEN
        result_data := family_data;
        RAISE NOTICE 'fn_apply_family_to_couple_coverage: ✓ Applied family values to couple coverage for % benefit', benefit_key;
        RAISE NOTICE 'fn_apply_family_to_couple_coverage: New couple data: %', result_data::TEXT;
    ELSE
        result_data := couple_data;
        RAISE NOTICE 'fn_apply_family_to_couple_coverage: ✗ Kept original couple data for % benefit', benefit_key;
    END IF;

    RAISE NOTICE '=== fn_apply_family_to_couple_coverage END ===';
    RETURN result_data;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'fn_apply_family_to_couple_coverage: Error occurred: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
        -- Return original couple data on error
        RETURN couple_data;
END;
$$;
    ]]>
</sql>
