<sql splitStatements="false">
    <![CDATA[
        CREATE OR REPLACE FUNCTION sandf.fn_get_renewal_charges_and_targeted_claims_multi_class(
    target_plan_uuid TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$

DECLARE
    v_plan_id INTEGER;
    quote_record RECORD;
    quote_json JSONB;
    carrier_name TEXT;
    carrier_order INTEGER;
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array TEXT[];

    -- Result structure variables
    section_list JSONB := '[]'::JSONB;
    renewal_section_list JSONB := '[]'::JSONB;

    -- Multi-class support variables
    employee_classes TEXT[];
    employee_class_count INTEGER;
    current_employee_class TEXT;

    -- Premium and volume variables (aggregated across all classes per carrier)
    carrier_totals JSONB := '{}'::jsonb;
    carrier_ehc_prem NUMERIC := 0;
    carrier_ehc_vol NUMERIC := 0;
    carrier_dental_prem NUMERIC := 0;
    carrier_dental_vol NUMERIC := 0;
    ehc_avg NUMERIC := 0;
    dental_avg NUMERIC := 0;

    -- Rating factor variables (per carrier - using first class found)
    trend_ehc NUMERIC := 0;
    ibnr_ehc NUMERIC := 0;
    tlr_ehc NUMERIC := 0;
    ibnr_dental NUMERIC := 0;
    tlr_dental NUMERIC := 0;
    amount NUMERIC := 0;
    renewal_percent NUMERIC := 0;

    -- Text variables for display
    trend_ehc_text TEXT;
    ibnr_ehc_text TEXT;
    tlr_ehc_text TEXT;
    ibnr_dental_text TEXT;
    tlr_dental_text TEXT;
    amount_text TEXT;
    renewal_percent_str TEXT;
    renewal_model_text TEXT;
    x RECORD;

    -- Helper function for safe numeric conversion
    safe_numeric_value NUMERIC;

    -- Coverage type processing
    coverage_order TEXT[] := ARRAY['single', 'couple', 'family'];
    coverage_type TEXT;
    subval JSONB;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;
BEGIN
    -- Get plan_id from plan_uuid
    SELECT plan_id INTO v_plan_id
    FROM sandf.plan
    WHERE plan_uuid = target_plan_uuid::uuid;

    IF v_plan_id IS NULL THEN
        RETURN jsonb_build_object(
            'targetedClaims', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            ),
            'renewalCharges', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            )
        );
    END IF;

    -- Step 1: Detect employee classes for this plan
    RAISE NOTICE '=== STEP 1: DETECTING EMPLOYEE CLASSES ===';
    RAISE NOTICE 'Plan UUID: %', target_plan_uuid;
    RAISE NOTICE 'User ID: %', user_id_param;
    RAISE NOTICE 'Includes Quotes UUID: %', includes_quotes_uuid;

    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = target_plan_uuid::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb
    AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid));

    RAISE NOTICE 'Found % employee classes: %', employee_class_count, employee_classes;
    RAISE NOTICE '=== END STEP 1 ===';
    RAISE NOTICE '';

    -- Check if we found any employee classes
    IF employee_class_count = 0 OR employee_classes IS NULL THEN
        RAISE NOTICE 'No employee classes found for plan: %', target_plan_uuid;
        RETURN jsonb_build_object(
            'targetedClaims', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            ),
            'renewalCharges', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            )
        );
    END IF;

    -- Step 2: Build carrier order map for all employee classes
    RAISE NOTICE '=== STEP 2: BUILDING CARRIER ORDER MAP ===';
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid,
               ec.name as employee_class_name
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = target_plan_uuid::uuid
        AND ec.name = ANY(employee_classes)
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
    LOOP
        carrier_name := quote_record.carrier_description;
        RAISE NOTICE 'Processing carrier order for: % (Class: %, Quote ID: %)',
            carrier_name, quote_record.employee_class_name, quote_record.quote_id;

        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            target_plan_uuid,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999
        );

        RAISE NOTICE 'Carrier % assigned order: %', carrier_name, carrier_order;

        carrier_order_map := carrier_order_map || jsonb_build_object(
            carrier_name,
            jsonb_build_object('order', carrier_order)
        );
    END LOOP;
    RAISE NOTICE '=== END STEP 2 ===';
    RAISE NOTICE '';

    -- Step 3: Build ordered carriers array and capture first carrier name
    RAISE NOTICE '=== STEP 3: BUILDING ORDERED CARRIERS ARRAY ===';
    SELECT array_agg(carriers.carrier_name ORDER BY (carrier_order_map -> carriers.carrier_name ->> 'order')::integer ASC)
    INTO ordered_carriers_array
    FROM (
        SELECT DISTINCT jsonb_object_keys(carrier_order_map) as carrier_name
    ) carriers;

    RAISE NOTICE 'Initial ordered carriers array: %', ordered_carriers_array;

    -- Capture the first carrier name for display purposes
    IF array_length(ordered_carriers_array, 1) > 0 THEN
        first_carrier_name := ordered_carriers_array[1];
        RAISE NOTICE 'First carrier name captured: %', first_carrier_name;

        -- Replace first carrier with "Current" in the display array
        ordered_carriers_array[1] := 'Current';
        RAISE NOTICE 'Updated carriers array with "Current": %', ordered_carriers_array;
    END IF;

    RAISE NOTICE 'Carrier order map: %', carrier_order_map;
    RAISE NOTICE 'Final ordered carriers array: %', ordered_carriers_array;
    RAISE NOTICE '=== END STEP 3 ===';
    RAISE NOTICE '';

    -- Check if we found any carriers
    IF ordered_carriers_array IS NULL OR array_length(ordered_carriers_array, 1) = 0 THEN
        RAISE NOTICE 'No carriers found for plan: %', target_plan_uuid;
        RETURN jsonb_build_object(
            'targetedClaims', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            ),
            'renewalCharges', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            )
        );
    END IF;

    -- Step 4: Process all quotes to aggregate data by carrier across all employee classes
    RAISE NOTICE '=== STEP 4: PROCESSING QUOTES FOR AGGREGATION ===';
    RAISE NOTICE 'Starting aggregation across all employee classes and carriers';

    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid,
               ec.name as employee_class_name
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = target_plan_uuid::uuid
        AND ec.name = ANY(employee_classes)
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
        ORDER BY
            COALESCE((carrier_order_map -> c.description ->> 'order')::integer, 999999) ASC,
            c.description ASC,
            ec.name ASC
    LOOP
        quote_json := quote_record.formatted_quote_details::jsonb;
        carrier_name := quote_record.carrier_description;
        current_employee_class := quote_record.employee_class_name;

        RAISE NOTICE '--- Processing carrier: % for class: % (Quote ID: %) ---',
            carrier_name, current_employee_class, quote_record.quote_id;

        -- Initialize carrier totals if not exists
        IF NOT (carrier_totals ? carrier_name) THEN
            RAISE NOTICE 'Initializing carrier totals for: %', carrier_name;
            carrier_totals := carrier_totals || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'ehc_prem', 0,
                    'ehc_vol', 0,
                    'dental_prem', 0,
                    'dental_vol', 0,
                    'rating_factors_set', false
                )
            );
        END IF;

        -- Get current totals for this carrier
        carrier_ehc_prem := COALESCE((carrier_totals -> carrier_name ->> 'ehc_prem')::NUMERIC, 0);
        carrier_ehc_vol := COALESCE((carrier_totals -> carrier_name ->> 'ehc_vol')::NUMERIC, 0);
        carrier_dental_prem := COALESCE((carrier_totals -> carrier_name ->> 'dental_prem')::NUMERIC, 0);
        carrier_dental_vol := COALESCE((carrier_totals -> carrier_name ->> 'dental_vol')::NUMERIC, 0);

        RAISE NOTICE 'Current totals for %: EHC Prem=%, EHC Vol=%, Dental Prem=%, Dental Vol=%',
            carrier_name, carrier_ehc_prem, carrier_ehc_vol, carrier_dental_prem, carrier_dental_vol;

        -- Extract rating factors only once per carrier (use first class found)
        IF NOT (carrier_totals -> carrier_name ->> 'rating_factors_set')::BOOLEAN THEN
            RAISE NOTICE 'Setting rating factors for carrier: % using class: %', carrier_name, current_employee_class;

            trend_ehc_text := quote_json #>> '{benefitPremiums,ratingFactors,extendedHealthRatingFactors,pooling}';
            BEGIN
                IF trend_ehc_text IS NULL OR TRIM(trend_ehc_text) = '' OR TRIM(trend_ehc_text) = '0' THEN
                    trend_ehc := 0;
                ELSE
                    trend_ehc := COALESCE(
                        (regexp_replace(TRIM(trend_ehc_text), '[^0-9\.-]', '', 'g'))::NUMERIC,
                        0
                    );
                END IF;
            EXCEPTION
                WHEN OTHERS THEN
                    trend_ehc := 0;
            END;

            ibnr_ehc_text := quote_json #>> '{benefitPremiums,ratingFactors,extendedHealthRatingFactors,ibnr}';
            BEGIN
                ibnr_ehc := CASE
                    WHEN ibnr_ehc_text IS NULL OR TRIM(ibnr_ehc_text) = '' OR TRIM(ibnr_ehc_text) = '0' THEN 0
                    ELSE COALESCE((regexp_replace(TRIM(ibnr_ehc_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                END;
            EXCEPTION
                WHEN OTHERS THEN
                    ibnr_ehc := 0;
            END;

            tlr_ehc_text := quote_json #>> '{benefitPremiums,ratingFactors,extendedHealthRatingFactors,targetLossRatio}';
            BEGIN
                tlr_ehc := CASE
                    WHEN tlr_ehc_text IS NULL OR TRIM(tlr_ehc_text) = '' OR TRIM(tlr_ehc_text) = '0' THEN 0
                    ELSE 100 - COALESCE((regexp_replace(TRIM(tlr_ehc_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                END;
            EXCEPTION
                WHEN OTHERS THEN
                    tlr_ehc := 0;
            END;

            ibnr_dental_text := quote_json #>> '{benefitPremiums,ratingFactors,dentalRatingFactors,ibnr}';
            BEGIN
                ibnr_dental := CASE
                    WHEN ibnr_dental_text IS NULL OR TRIM(ibnr_dental_text) = '' OR TRIM(ibnr_dental_text) = '0' THEN 0
                    ELSE COALESCE((regexp_replace(TRIM(ibnr_dental_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                END;
            EXCEPTION
                WHEN OTHERS THEN
                    ibnr_dental := 0;
            END;

            tlr_dental_text := quote_json #>> '{benefitPremiums,ratingFactors,dentalRatingFactors,targetLossRatio}';
            BEGIN
                tlr_dental := CASE
                    WHEN tlr_dental_text IS NULL OR TRIM(tlr_dental_text) = '' OR TRIM(tlr_dental_text) = '0' THEN 0
                    ELSE 100 - COALESCE((regexp_replace(TRIM(tlr_dental_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                END;
            EXCEPTION
                WHEN OTHERS THEN
                    tlr_dental := 0;
            END;

            -- Extract renewalModel for renewal charge calculation
            renewal_model_text := quote_json #>> '{benefitPremiums,rateGuarantees,renewalModel}';

            -- Store rating factors and renewalModel in carrier totals
            carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'trend_ehc'], to_jsonb(trend_ehc));
            carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'ibnr_ehc'], to_jsonb(ibnr_ehc));
            carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'tlr_ehc'], to_jsonb(tlr_ehc));
            carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'ibnr_dental'], to_jsonb(ibnr_dental));
            carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'tlr_dental'], to_jsonb(tlr_dental));
            carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'renewal_model'], to_jsonb(COALESCE(renewal_model_text, '')));
            carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'rating_factors_set'], to_jsonb(true));

            RAISE NOTICE 'Rating factors set for %: trend_ehc=%, ibnr_ehc=%, tlr_ehc=%, ibnr_dental=%, tlr_dental=%, renewal_model=%',
                carrier_name, trend_ehc, ibnr_ehc, tlr_ehc, ibnr_dental, tlr_dental, renewal_model_text;
        ELSE
            RAISE NOTICE 'Rating factors already set for carrier: %, skipping', carrier_name;
        END IF;

        -- Calculate EHC volumes and premiums for this class and add to carrier totals
        IF quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'single' OR
           quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'couple' OR
           quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'family' THEN
            -- Apply special logic for extended health couple coverage using global function
            IF quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'couple' AND quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'family' THEN
                DECLARE
                    updated_couple_data_rtc_multi_ehc JSONB;
                BEGIN
                    updated_couple_data_rtc_multi_ehc := sandf.fn_apply_family_to_couple_coverage(
                        'extendedHealth',
                        quote_json #> '{benefitPremiums,extendedHealthPremium,couple}',
                        quote_json #> '{benefitPremiums,extendedHealthPremium,family}'
                    );

                    -- Update the couple data if it was changed
                    IF updated_couple_data_rtc_multi_ehc != quote_json #> '{benefitPremiums,extendedHealthPremium,couple}' THEN
                        quote_json := jsonb_set(quote_json, '{benefitPremiums,extendedHealthPremium,couple}', updated_couple_data_rtc_multi_ehc);
                        RAISE NOTICE 'Renewal Target Claims Multi-class: Applied family values to couple coverage for extendedHealth';
                    END IF;
                END;
            END IF;

            -- Handle nested structure (single/couple/family)
            FOREACH coverage_type IN ARRAY coverage_order
            LOOP
                IF quote_json #> '{benefitPremiums,extendedHealthPremium}' ? coverage_type THEN
                    subval := quote_json #> ARRAY['benefitPremiums', 'extendedHealthPremium', coverage_type];

                    BEGIN
                        safe_numeric_value := CASE
                            WHEN subval ->> 'volume' IS NULL OR TRIM(subval ->> 'volume') = '' THEN 0
                            ELSE COALESCE((regexp_replace(TRIM(subval ->> 'volume'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                        END;
                        carrier_ehc_vol := carrier_ehc_vol + safe_numeric_value;
                    EXCEPTION
                        WHEN OTHERS THEN
                            carrier_ehc_vol := carrier_ehc_vol + 0;
                    END;

                    BEGIN
                        safe_numeric_value := CASE
                            WHEN subval ->> 'premium' IS NULL OR TRIM(subval ->> 'premium') = '' THEN 0
                            ELSE COALESCE((regexp_replace(TRIM(subval ->> 'premium'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                        END;
                        carrier_ehc_prem := carrier_ehc_prem + safe_numeric_value;
                    EXCEPTION
                        WHEN OTHERS THEN
                            carrier_ehc_prem := carrier_ehc_prem + 0;
                    END;

                    RAISE NOTICE 'EHC % for %: Volume: %, Premium: %, Carrier Total Volume: %, Carrier Total Premium: %',
                        coverage_type, current_employee_class, subval ->> 'volume', subval ->> 'premium', carrier_ehc_vol, carrier_ehc_prem;
                END IF;
            END LOOP;
        ELSE
            -- Handle direct structure (legacy format)
            FOR x IN SELECT * FROM jsonb_each(quote_json #> '{benefitPremiums,extendedHealthPremium}')
            LOOP
                BEGIN
                    safe_numeric_value := CASE
                        WHEN x.value ->> 'volume' IS NULL OR TRIM(x.value ->> 'volume') = '' THEN 0
                        ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'volume'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                    END;
                    carrier_ehc_vol := carrier_ehc_vol + safe_numeric_value;
                EXCEPTION
                    WHEN OTHERS THEN
                        carrier_ehc_vol := carrier_ehc_vol + 0;
                END;

                BEGIN
                    safe_numeric_value := CASE
                        WHEN x.value ->> 'premium' IS NULL OR TRIM(x.value ->> 'premium') = '' THEN 0
                        ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'premium'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                    END;
                    carrier_ehc_prem := carrier_ehc_prem + safe_numeric_value;
                EXCEPTION
                    WHEN OTHERS THEN
                        carrier_ehc_prem := carrier_ehc_prem + 0;
                END;

                RAISE NOTICE 'EHC Key % for %: Volume: %, Premium: %, Carrier Total Volume: %, Carrier Total Premium: %',
                    x.key, current_employee_class, x.value ->> 'volume', x.value ->> 'premium', carrier_ehc_vol, carrier_ehc_prem;
            END LOOP;
        END IF;

        -- Calculate Dental volumes and premiums for this class and add to carrier totals
        IF quote_json #> '{benefitPremiums,dentalCarePremium}' ? 'single' OR
           quote_json #> '{benefitPremiums,dentalCarePremium}' ? 'couple' OR
           quote_json #> '{benefitPremiums,dentalCarePremium}' ? 'family' THEN
            -- Apply special logic for dental couple coverage using global function
            IF quote_json #> '{benefitPremiums,dentalCarePremium}' ? 'couple' AND quote_json #> '{benefitPremiums,dentalCarePremium}' ? 'family' THEN
                DECLARE
                    updated_couple_data_rtc_multi_dental JSONB;
                BEGIN
                    updated_couple_data_rtc_multi_dental := sandf.fn_apply_family_to_couple_coverage(
                        'dental',
                        quote_json #> '{benefitPremiums,dentalCarePremium,couple}',
                        quote_json #> '{benefitPremiums,dentalCarePremium,family}'
                    );

                    -- Update the couple data if it was changed
                    IF updated_couple_data_rtc_multi_dental != quote_json #> '{benefitPremiums,dentalCarePremium,couple}' THEN
                        quote_json := jsonb_set(quote_json, '{benefitPremiums,dentalCarePremium,couple}', updated_couple_data_rtc_multi_dental);
                        RAISE NOTICE 'Renewal Target Claims Multi-class: Applied family values to couple coverage for dental';
                    END IF;
                END;
            END IF;

            -- Handle nested structure (single/couple/family)
            FOREACH coverage_type IN ARRAY coverage_order
            LOOP
                IF quote_json #> '{benefitPremiums,dentalCarePremium}' ? coverage_type THEN
                    subval := quote_json #> ARRAY['benefitPremiums', 'dentalCarePremium', coverage_type];

                    BEGIN
                        safe_numeric_value := CASE
                            WHEN subval ->> 'volume' IS NULL OR TRIM(subval ->> 'volume') = '' THEN 0
                            ELSE COALESCE((regexp_replace(TRIM(subval ->> 'volume'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                        END;
                        carrier_dental_vol := carrier_dental_vol + safe_numeric_value;
                    EXCEPTION
                        WHEN OTHERS THEN
                            carrier_dental_vol := carrier_dental_vol + 0;
                    END;

                    BEGIN
                        safe_numeric_value := CASE
                            WHEN subval ->> 'premium' IS NULL OR TRIM(subval ->> 'premium') = '' THEN 0
                            ELSE COALESCE((regexp_replace(TRIM(subval ->> 'premium'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                        END;
                        carrier_dental_prem := carrier_dental_prem + safe_numeric_value;
                    EXCEPTION
                        WHEN OTHERS THEN
                            carrier_dental_prem := carrier_dental_prem + 0;
                    END;

                    RAISE NOTICE 'Dental % for %: Volume: %, Premium: %, Carrier Total Volume: %, Carrier Total Premium: %',
                        coverage_type, current_employee_class, subval ->> 'volume', subval ->> 'premium', carrier_dental_vol, carrier_dental_prem;
                END IF;
            END LOOP;
        ELSE
            -- Handle direct structure (legacy format)
            FOR x IN SELECT * FROM jsonb_each(quote_json #> '{benefitPremiums,dentalCarePremium}')
            LOOP
                BEGIN
                    safe_numeric_value := CASE
                        WHEN x.value ->> 'volume' IS NULL OR TRIM(x.value ->> 'volume') = '' THEN 0
                        ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'volume'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                    END;
                    carrier_dental_vol := carrier_dental_vol + safe_numeric_value;
                EXCEPTION
                    WHEN OTHERS THEN
                        carrier_dental_vol := carrier_dental_vol + 0;
                END;

                BEGIN
                    safe_numeric_value := CASE
                        WHEN x.value ->> 'premium' IS NULL OR TRIM(x.value ->> 'premium') = '' THEN 0
                        ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'premium'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                    END;
                    carrier_dental_prem := carrier_dental_prem + safe_numeric_value;
                EXCEPTION
                    WHEN OTHERS THEN
                        carrier_dental_prem := carrier_dental_prem + 0;
                END;

                RAISE NOTICE 'Dental Key % for %: Volume: %, Premium: %, Carrier Total Volume: %, Carrier Total Premium: %',
                    x.key, current_employee_class, x.value ->> 'volume', x.value ->> 'premium', carrier_dental_vol, carrier_dental_prem;
            END LOOP;
        END IF;

        -- Update carrier totals
        carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'ehc_prem'], to_jsonb(carrier_ehc_prem));
        carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'ehc_vol'], to_jsonb(carrier_ehc_vol));
        carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'dental_prem'], to_jsonb(carrier_dental_prem));
        carrier_totals := jsonb_set(carrier_totals, ARRAY[carrier_name, 'dental_vol'], to_jsonb(carrier_dental_vol));

        RAISE NOTICE 'Updated carrier % totals after processing class %: EHC Vol=%, EHC Prem=%, Dental Vol=%, Dental Prem=%',
            carrier_name, current_employee_class, carrier_ehc_vol, carrier_ehc_prem, carrier_dental_vol, carrier_dental_prem;
        RAISE NOTICE '--- End processing for carrier % class % ---', carrier_name, current_employee_class;
        RAISE NOTICE '';
    END LOOP;

    RAISE NOTICE '=== END STEP 4 ===';
    RAISE NOTICE '';

    -- Step 5: Calculate renewal targets for each carrier using aggregated totals
    RAISE NOTICE '=== STEP 5: CALCULATING RENEWAL TARGETS ===';
    RAISE NOTICE 'Final carrier totals JSON: %', carrier_totals;

    -- Log EHC and Dental totals by carrier
    RAISE NOTICE '=== AGGREGATED TOTALS BY CARRIER (ACROSS ALL EMPLOYEE CLASSES) ===';
    FOR carrier_name IN SELECT jsonb_object_keys(carrier_totals)
    LOOP
        carrier_ehc_prem := COALESCE((carrier_totals -> carrier_name ->> 'ehc_prem')::NUMERIC, 0);
        carrier_ehc_vol := COALESCE((carrier_totals -> carrier_name ->> 'ehc_vol')::NUMERIC, 0);
        carrier_dental_prem := COALESCE((carrier_totals -> carrier_name ->> 'dental_prem')::NUMERIC, 0);
        carrier_dental_vol := COALESCE((carrier_totals -> carrier_name ->> 'dental_vol')::NUMERIC, 0);

        RAISE NOTICE 'CARRIER: %', carrier_name;
        RAISE NOTICE '  EHC Total Premium (All Classes): $%', COALESCE(TO_CHAR(carrier_ehc_prem, 'FM999,999,999.00'), '0.00');
        RAISE NOTICE '  EHC Total Volume (All Classes): %', COALESCE(TO_CHAR(carrier_ehc_vol, 'FM999,999,999'), '0');
        RAISE NOTICE '  Dental Total Premium (All Classes): $%', COALESCE(TO_CHAR(carrier_dental_prem, 'FM999,999,999.00'), '0.00');
        RAISE NOTICE '  Dental Total Volume (All Classes): %', COALESCE(TO_CHAR(carrier_dental_vol, 'FM999,999,999'), '0');
        RAISE NOTICE '  Combined Premium Total (All Classes): $%', COALESCE(TO_CHAR(carrier_ehc_prem + carrier_dental_prem, 'FM999,999,999.00'), '0.00');
        RAISE NOTICE '  Combined Volume Total (All Classes): %', COALESCE(TO_CHAR(carrier_ehc_vol + carrier_dental_vol, 'FM999,999,999'), '0');

        -- Show rating factors for this carrier
        RAISE NOTICE '  Rating Factors:';
        RAISE NOTICE '    Trend EHC: %', COALESCE((carrier_totals -> carrier_name ->> 'trend_ehc')::TEXT, '0');
        RAISE NOTICE '    IBNR EHC: %', COALESCE((carrier_totals -> carrier_name ->> 'ibnr_ehc')::TEXT, '0');
        RAISE NOTICE '    TLR EHC: %', COALESCE((carrier_totals -> carrier_name ->> 'tlr_ehc')::TEXT, '0');
        RAISE NOTICE '    IBNR Dental: %', COALESCE((carrier_totals -> carrier_name ->> 'ibnr_dental')::TEXT, '0');
        RAISE NOTICE '    TLR Dental: %', COALESCE((carrier_totals -> carrier_name ->> 'tlr_dental')::TEXT, '0');
        RAISE NOTICE '    Renewal Model: %', COALESCE(carrier_totals -> carrier_name ->> 'renewal_model', 'N/A');
        RAISE NOTICE '---';
    END LOOP;
    RAISE NOTICE '=== END OF AGGREGATED CARRIER TOTALS ===';
    RAISE NOTICE '';

    RAISE NOTICE '=== CALCULATING TARGETED CLAIMS AND RENEWAL CHARGES ===';
    FOR carrier_name IN SELECT jsonb_object_keys(carrier_totals)
    LOOP
        RAISE NOTICE '--- Processing calculations for carrier: % ---', carrier_name;

        -- Get aggregated totals for this carrier
        carrier_ehc_prem := COALESCE((carrier_totals -> carrier_name ->> 'ehc_prem')::NUMERIC, 0);
        carrier_ehc_vol := COALESCE((carrier_totals -> carrier_name ->> 'ehc_vol')::NUMERIC, 0);
        carrier_dental_prem := COALESCE((carrier_totals -> carrier_name ->> 'dental_prem')::NUMERIC, 0);
        carrier_dental_vol := COALESCE((carrier_totals -> carrier_name ->> 'dental_vol')::NUMERIC, 0);

        RAISE NOTICE 'Input values for %: EHC Prem=%, EHC Vol=%, Dental Prem=%, Dental Vol=%',
            carrier_name, carrier_ehc_prem, carrier_ehc_vol, carrier_dental_prem, carrier_dental_vol;

        -- Get rating factors and renewalModel for this carrier
        trend_ehc := COALESCE((carrier_totals -> carrier_name ->> 'trend_ehc')::NUMERIC, 0);
        ibnr_ehc := COALESCE((carrier_totals -> carrier_name ->> 'ibnr_ehc')::NUMERIC, 0);
        tlr_ehc := COALESCE((carrier_totals -> carrier_name ->> 'tlr_ehc')::NUMERIC, 0);
        ibnr_dental := COALESCE((carrier_totals -> carrier_name ->> 'ibnr_dental')::NUMERIC, 0);
        tlr_dental := COALESCE((carrier_totals -> carrier_name ->> 'tlr_dental')::NUMERIC, 0);
        renewal_model_text := COALESCE(carrier_totals -> carrier_name ->> 'renewal_model', '');

        RAISE NOTICE 'Rating factors for %: trend_ehc=%, ibnr_ehc=%, tlr_ehc=%, ibnr_dental=%, tlr_dental=%',
            carrier_name, trend_ehc, ibnr_ehc, tlr_ehc, ibnr_dental, tlr_dental;

        -- Calculate average premiums per unit volume (monthly * 12)
        ehc_avg := CASE WHEN carrier_ehc_vol > 0 THEN (carrier_ehc_prem / carrier_ehc_vol) * 12 ELSE 0 END;
        dental_avg := CASE WHEN carrier_dental_vol > 0 THEN (carrier_dental_prem / carrier_dental_vol) * 12 ELSE 0 END;

        RAISE NOTICE 'Average calculations for %: ehc_avg=% (monthly premium * 12), dental_avg=% (monthly premium * 12)',
            carrier_name, ehc_avg, dental_avg;

        -- Calculate targetClaimsValue with renewal charges
        RAISE NOTICE 'Starting targeted claims calculation for %', carrier_name;
        IF (ehc_avg = 0 AND dental_avg = 0) THEN
            RAISE NOTICE 'Both EHC and Dental averages are 0, setting amount to $0';
            amount_text := '$0';
            amount := 0;
        ELSE
            BEGIN
                -- Perform calculation with detailed logging
                RAISE NOTICE 'Calculation breakdown for %:', carrier_name;
                RAISE NOTICE '  EHC Component: % * (1 - %/100) * (1 - %/100) * (1 - %/100)',
                    ehc_avg, trend_ehc, ibnr_ehc, tlr_ehc;
                RAISE NOTICE '  Dental Component: % * (1 - %/100) * (1 - %/100)',
                    dental_avg, ibnr_dental, tlr_dental;

                amount := (ehc_avg * (1 - (trend_ehc / 100)) * (1 - (ibnr_ehc / 100)) * (1 - (tlr_ehc / 100))) +
                         (dental_avg * (1 - (ibnr_dental / 100)) * (1 - (tlr_dental / 100)));

                RAISE NOTICE 'Carrier % calculated targeted claims amount: %', carrier_name, amount;
                amount_text := sandf.fn_format_currency_with_symbol_java_style(amount);
                RAISE NOTICE 'Formatted amount text: %', amount_text;
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Exception occurred during amount calculation for carrier %: %', carrier_name, SQLERRM;
                    amount_text := '$0';
                    amount := 0;
            END;
        END IF;

        -- Add to section lists
        section_list := section_list || jsonb_build_object('amount', amount_text);

        -- Calculate and format renewal charge percentage
        RAISE NOTICE 'Starting renewal charge calculation for %', carrier_name;
        RAISE NOTICE 'Renewal model text: "%"', renewal_model_text;

        -- First check renewalModel - if 'Partially Pooled' or 'Pooled', return 'Pooled'
        IF renewal_model_text IS NOT NULL AND TRIM(renewal_model_text) != '' AND
           (TRIM(renewal_model_text) = 'Partially Pooled' OR TRIM(renewal_model_text) = 'Pooled') THEN
            renewal_percent_str := 'Pooled';
            RAISE NOTICE 'Renewal model is pooled, setting renewal charge to "Pooled"';
        ELSIF amount_text = '$0' OR (ehc_avg + dental_avg) = 0 THEN
            renewal_percent_str := '0%';
            RAISE NOTICE 'Amount is $0 or total average is 0, setting renewal charge to 0%%';
        ELSE
            BEGIN
                RAISE NOTICE 'Calculating renewal percentage: (1 - (% / %)) * 100', amount, (ehc_avg + dental_avg);
                renewal_percent := (1 - (amount / (ehc_avg + dental_avg))) * 100;
                RAISE NOTICE 'Raw renewal percentage: %', renewal_percent;
                renewal_percent_str := COALESCE(TO_CHAR(CEIL(renewal_percent * 10) / 10, 'FM999990.0'), '0') || '%';
                RAISE NOTICE 'Formatted renewal percentage: %', renewal_percent_str;
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Exception during renewal percentage calculation: %', SQLERRM;
                    renewal_percent_str := '0%';
            END;
        END IF;

        renewal_section_list := renewal_section_list || jsonb_build_object(
            'renewalCharge', renewal_percent_str
        );

        RAISE NOTICE 'Final results for carrier %: targeted_claims=%, renewal_charge=%',
            carrier_name, amount_text, renewal_percent_str;
        RAISE NOTICE '--- End calculations for carrier % ---', carrier_name;
        RAISE NOTICE '';
    END LOOP;

    RAISE NOTICE '=== END STEP 5 ===';
    RAISE NOTICE '';

    -- Debug final results
    RAISE NOTICE '=== FINAL RESULTS SUMMARY ===';
    RAISE NOTICE 'Final section_list (targeted claims): %', section_list;
    RAISE NOTICE 'Final renewal_section_list (renewal charges): %', renewal_section_list;
    RAISE NOTICE 'Final ordered_carriers_array: %', ordered_carriers_array;
    RAISE NOTICE '=== END FINAL RESULTS SUMMARY ===';
    RAISE NOTICE '';

    -- Return final result with ordered carriers
    RETURN jsonb_build_object(
        'targetedClaims', jsonb_build_object(
            'carriers', ordered_carriers_array,
            'sections', section_list
        ),
        'renewalCharges', jsonb_build_object(
            'carriers', ordered_carriers_array,
            'sections', renewal_section_list
        )
    );
EXCEPTION
    WHEN OTHERS THEN
        -- Global error handler - return empty result
        RAISE NOTICE 'Error in function: %', SQLERRM;
        RETURN jsonb_build_object(
            'targetedClaims', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            ),
            'renewalCharges', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            )
        );
END;
$$;
]]>
        </sql>
