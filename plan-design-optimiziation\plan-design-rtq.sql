  <sql splitStatements="false">
            <![CDATA[
        CREATE OR REPLACE FUNCTION sandf.fn_get_plan_design_report_RTQ(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    plan_details_key TEXT := 'planDetails';
    rtq_name TEXT := 'RTQ';
    quote_record RECORD;
    plan_details JSONB;
    fields JSONB;
    field_item JSONB;
    carriers_array JSONB := '[]'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefits_array JSONB;
    benefit_obj JSONB;
    carrier_name TEXT;
    group_name TEXT;
    group_display_name TEXT;
    benefit_name TEXT;
    benefit_key TEXT;
    benefit_values JSONB := '{}'::jsonb;
    field_details JSONB;
    field_detail JSONB;
    carrier_value TEXT;
    section_map JSONB := '{}'::jsonb;
    section_names TEXT[];
    section_name TEXT;
    section_id TEXT;
    section_display_name TEXT;
    benefit_map JSONB := '{}'::jsonb;
    benefit_keys TEXT[];
    benefit_key_item TEXT;
    section_order_record RECORD;
    benefit_order_record RECORD;

    -- Variables for skip condition logic
    skip_fields TEXT[] := ARRAY['maximumLife', 'maximumADAD'];
    coverage_life_values JSONB := '{}'::jsonb;
    field_values_to_check JSONB := '{}'::jsonb;
    should_skip_field BOOLEAN;
    carrier_check TEXT;
    coverage_value TEXT;
    field_value TEXT;

    -- Variables for carrier ordering
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order INTEGER;
    quote_uuid_val UUID;

    -- Pagination variables
    MAX_BENEFITS_PER_PAGE INTEGER := 10;
    all_benefits JSONB := '[]'::jsonb;
    all_sections JSONB := '[]'::jsonb;
    current_page_benefits INTEGER := 0;
    current_page_sections JSONB := '[]'::jsonb;
    current_section_benefits JSONB := '[]'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    total_benefits INTEGER;
    current_section_name TEXT;
    current_section_id TEXT;
    current_section_display_name TEXT;
    section_idx INTEGER;
    benefit_idx INTEGER;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;

    -- Configuration variables for filtering and ordering
    config_json JSONB;
    config_plan_details JSONB;
    config_overview JSONB;
    config_section_key TEXT;
    config_section_info JSONB;
    config_display_items JSONB;
    mapped_section_key TEXT;
    carrier_has_data BOOLEAN;
    config_section_keys TEXT[];
    config_section_order INTEGER;

BEGIN
    -- Load configuration from database using helper function
    config_json := sandf.load_plan_design_config();
    config_plan_details := sandf.get_plan_details_config(config_json);

    -- Get the order of sections from config_plan_details keys (maintains order from config.json)
    config_section_keys := sandf.get_ordered_section_keys(config_plan_details);

    -- Build carrier order map and get first carrier using helper functions
    carrier_order_map := sandf.build_carrier_order_map(plan_uuid_param, user_id_param, 'RTQ');
    first_carrier_name := sandf.get_first_carrier_name(carrier_order_map);

    -- Second pass: Process quotes to build plan data with proper ordering
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = 'RTQ'
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        -- Order using the new function
        ORDER BY
            sandf.get_user_preference_order(
                user_id_param,
                plan_uuid_param,
                q.quote_id,
                q.quote_uuid,
                999999
            ) ASC,
            c.description ASC
    LOOP
        carrier_name := quote_record.carrier_description;

        -- Continue with existing logic for processing plan details
        plan_details := quote_record.formatted_quote_details -> plan_details_key;

        IF plan_details IS NOT NULL AND jsonb_typeof(plan_details) = 'object' THEN
            fields := plan_details -> 'fields';
            IF fields IS NOT NULL AND jsonb_typeof(fields) = 'array' THEN
                FOR field_item IN SELECT jsonb_array_elements(fields)
                LOOP
                    group_name := field_item ->> 'groupName';
                    IF group_name IS NULL THEN
                        group_name := field_item ->> 'name';
                    END IF;

                    -- Check if this section is in our configuration using helper function
                    SELECT * INTO mapped_section_key, config_section_info
                    FROM sandf.get_section_config_info(group_name, config_plan_details, includes_param, excludes_param);

                    -- Only process if section is in configuration
                    IF config_section_info IS NOT NULL THEN

                        group_display_name := config_section_info ->> mapped_section_key;
                        config_display_items := config_section_info -> 'displayItems';

                        section_map := section_map || jsonb_build_object(mapped_section_key, group_display_name);

                        field_details := field_item -> 'fields';
                        IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                            FOR field_detail IN SELECT jsonb_array_elements(field_details)
                            LOOP
                                benefit_key := field_detail ->> 'name';
                                carrier_value := field_detail ->> 'value';

                                -- Only process fields that are in displayItems configuration
                                IF config_display_items ? benefit_key THEN
                                    benefit_name := config_display_items ->> benefit_key;

                                    -- Validate and clean carrier value using helper function
                                    carrier_value := sandf.validate_carrier_value(carrier_value);

                                    -- Get display carrier name using helper function
                                    display_carrier_name := sandf.get_display_carrier_name(carrier_name, first_carrier_name);

                                    -- Store coverageLife values for comparison using helper function
                                    IF benefit_key = 'coverageLife' THEN
                                        coverage_life_values := sandf.store_coverage_life_value(coverage_life_values, display_carrier_name, carrier_value);
                                    END IF;

                                    -- Store values for fields that need to be checked against coverageLife using helper function
                                    IF benefit_key = ANY(skip_fields) THEN
                                        field_values_to_check := sandf.store_field_value_for_checking(field_values_to_check, benefit_key, display_carrier_name, carrier_value);
                                    END IF;

                                    benefit_key_item := mapped_section_key || '.' || benefit_key;

                                    -- Update benefit map using helper function
                                    benefit_map := sandf.update_benefit_map(
                                        benefit_map,
                                        benefit_key_item,
                                        benefit_name,
                                        benefit_key,
                                        mapped_section_key,
                                        display_carrier_name,
                                        carrier_value
                                    );
                                END IF;
                            END LOOP;
                        END IF;

                        -- Handle ADAD section separately for lifeInsuranceADAD using helper function
                        SELECT * INTO config_section_info FROM sandf.get_adad_section_config(group_name, config_plan_details);
                        IF config_section_info IS NOT NULL THEN
                            group_display_name := config_section_info ->> 'ADAD';
                            config_display_items := config_section_info -> 'displayItems';

                            -- Add ADAD section mapping
                            section_map := section_map || jsonb_build_object('ADAD', group_display_name);

                            -- Process ADAD fields
                            field_details := field_item -> 'fields';
                            IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                                FOR field_detail IN SELECT jsonb_array_elements(field_details)
                                LOOP
                                    benefit_key := field_detail ->> 'name';
                                    carrier_value := field_detail ->> 'value';

                                    -- Only process ADAD fields that are in displayItems configuration
                                    IF config_display_items ? benefit_key THEN
                                        benefit_name := config_display_items ->> benefit_key;

                                        -- Validate and clean carrier value using helper function
                                        carrier_value := sandf.validate_carrier_value(carrier_value);

                                        -- Get display carrier name using helper function
                                        display_carrier_name := sandf.get_display_carrier_name(carrier_name, first_carrier_name);

                                        -- Store values for fields that need to be checked against coverageLife using helper function
                                        IF benefit_key = ANY(skip_fields) THEN
                                            field_values_to_check := sandf.store_field_value_for_checking(field_values_to_check, benefit_key, display_carrier_name, carrier_value);
                                        END IF;

                                        benefit_key_item := 'ADAD' || '.' || benefit_key;

                                        -- Update benefit map using helper function
                                        benefit_map := sandf.update_benefit_map(
                                            benefit_map,
                                            benefit_key_item,
                                            benefit_name,
                                            benefit_key,
                                            'ADAD',
                                            display_carrier_name,
                                            carrier_value
                                        );
                                    END IF;
                                END LOOP;
                            END IF;
                        END IF;
                    END IF;
                END LOOP;
            ELSE
                -- Fallback to simple key-value object (existing logic)
                DECLARE
                    benefit_data JSONB;
                    field_key TEXT;
                    show_value TEXT;
                BEGIN
                    FOR benefit_key IN SELECT jsonb_object_keys(plan_details)
                    LOOP
                        -- Check if this section is in our configuration using helper function
                        SELECT * INTO mapped_section_key, config_section_info
                        FROM sandf.get_section_config_info(benefit_key, config_plan_details, includes_param, excludes_param);

                        -- Only process if section is in configuration
                        IF config_section_info IS NOT NULL THEN

                            benefit_data := plan_details -> benefit_key;

                            IF benefit_data IS NULL OR jsonb_typeof(benefit_data) != 'object' THEN
                                CONTINUE;
                            END IF;

                            show_value := benefit_data ->> 'show';
                            IF show_value IS NOT NULL AND show_value::boolean = false THEN
                                CONTINUE;
                            END IF;

                            section_display_name := config_section_info ->> mapped_section_key;
                            config_display_items := config_section_info -> 'displayItems';

                            section_map := section_map || jsonb_build_object(mapped_section_key, section_display_name);

                            FOR field_key IN SELECT jsonb_object_keys(benefit_data)
                            LOOP
                                IF field_key = 'show' THEN
                                    CONTINUE;
                                END IF;

                                -- Only process fields that are in displayItems configuration
                                IF config_display_items ? field_key THEN
                                    benefit_name := config_display_items ->> field_key;

                                    carrier_value := benefit_data ->> field_key;
                                    -- Validate and clean carrier value using helper function
                                    carrier_value := sandf.validate_carrier_value(carrier_value);

                                    -- Get display carrier name using helper function
                                    display_carrier_name := sandf.get_display_carrier_name(carrier_name, first_carrier_name);

                                    -- Store coverageLife values for comparison using helper function
                                    IF field_key = 'coverageLife' THEN
                                        coverage_life_values := sandf.store_coverage_life_value(coverage_life_values, display_carrier_name, carrier_value);
                                    END IF;

                                    -- Store values for fields that need to be checked against coverageLife using helper function
                                    IF field_key = ANY(skip_fields) THEN
                                        field_values_to_check := sandf.store_field_value_for_checking(field_values_to_check, field_key, display_carrier_name, carrier_value);
                                    END IF;

                                    benefit_key_item := mapped_section_key || '.' || field_key;

                                    -- Update benefit map using helper function
                                    benefit_map := sandf.update_benefit_map(
                                        benefit_map,
                                        benefit_key_item,
                                        benefit_name,
                                        field_key,
                                        mapped_section_key,
                                        display_carrier_name,
                                        carrier_value
                                    );
                                END IF;
                            END LOOP;

                            -- Handle ADAD section separately for lifeInsuranceADAD using helper function
                            SELECT * INTO config_section_info FROM sandf.get_adad_section_config(benefit_key, config_plan_details);
                            IF config_section_info IS NOT NULL THEN
                                section_display_name := config_section_info ->> 'ADAD';
                                config_display_items := config_section_info -> 'displayItems';

                                -- Add ADAD section mapping
                                section_map := section_map || jsonb_build_object('ADAD', section_display_name);

                                -- Process ADAD fields
                                FOR field_key IN SELECT jsonb_object_keys(benefit_data)
                                LOOP
                                    IF field_key = 'show' THEN
                                        CONTINUE;
                                    END IF;

                                    -- Only process ADAD fields that are in displayItems configuration
                                    IF config_display_items ? field_key THEN
                                        benefit_name := config_display_items ->> field_key;

                                        carrier_value := benefit_data ->> field_key;
                                        -- Validate and clean carrier value using helper function
                                        carrier_value := sandf.validate_carrier_value(carrier_value);

                                        -- Get display carrier name using helper function
                                        display_carrier_name := sandf.get_display_carrier_name(carrier_name, first_carrier_name);

                                        -- Store values for fields that need to be checked against coverageLife using helper function
                                        IF field_key = ANY(skip_fields) THEN
                                            field_values_to_check := sandf.store_field_value_for_checking(field_values_to_check, field_key, display_carrier_name, carrier_value);
                                        END IF;

                                        benefit_key_item := 'ADAD' || '.' || field_key;

                                        -- Update benefit map using helper function
                                        benefit_map := sandf.update_benefit_map(
                                            benefit_map,
                                            benefit_key_item,
                                            benefit_name,
                                            field_key,
                                            'ADAD',
                                            display_carrier_name,
                                            carrier_value
                                        );
                                    END IF;
                                END LOOP;
                            END IF;
                        END IF;
                    END LOOP;
                END;
            END IF;
        END IF;
    END LOOP;

    -- Build ordered carriers array using helper function
    ordered_carriers_array := sandf.build_ordered_carriers_array(carrier_order_map, first_carrier_name);

    -- Build all sections with skip logic applied using helper function
    all_sections := sandf.build_sections_with_skip_logic(
        section_map,
        benefit_map,
        config_plan_details,
        skip_fields,
        coverage_life_values,
        field_values_to_check
    );

    -- Count total benefits across all sections using helper function
    total_benefits := sandf.count_total_benefits(all_sections);

    -- If total benefits <= MAX_BENEFITS_PER_PAGE, return single page using helper function
    IF total_benefits <= MAX_BENEFITS_PER_PAGE THEN
        RETURN sandf.build_single_page_result(ordered_carriers_array, all_sections)::text;
    END IF;

    -- Otherwise, paginate by redistributing benefits across pages
    current_page_benefits := 0;
    current_page_sections := '[]'::jsonb;
    current_section_benefits := '[]'::jsonb;
    current_section_name := '';

    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        section_obj := all_sections -> section_idx;
        section_name := section_obj ->> 'name';
        section_id := section_obj ->> 'id';
        benefits_array := section_obj -> 'benefits';

        FOR benefit_idx IN 0..jsonb_array_length(benefits_array)-1 LOOP
            benefit_obj := benefits_array -> benefit_idx;

            -- If we're starting a new section or continuing the same section
            IF current_section_name != section_name THEN
                -- Finish previous section if exists
                IF current_section_name != '' AND jsonb_array_length(current_section_benefits) > 0 THEN
                    current_page_sections := current_page_sections || jsonb_build_array(
                        jsonb_build_object(
                            'name', current_section_display_name,
                            'id', current_section_id,
                            'benefits', current_section_benefits
                        )
                    );
                END IF;

                -- Start new section
                current_section_name := section_name;
                current_section_id := section_id;
                current_section_display_name := section_name;
                current_section_benefits := '[]'::jsonb;
            END IF;

            -- Check if adding this benefit would exceed page limit
            IF current_page_benefits >= MAX_BENEFITS_PER_PAGE THEN
                -- Finish current section
                IF jsonb_array_length(current_section_benefits) > 0 THEN
                    current_page_sections := current_page_sections || jsonb_build_array(
                        jsonb_build_object(
                            'name', current_section_display_name,
                            'id', current_section_id,
                            'benefits', current_section_benefits
                        )
                    );
                END IF;

                -- Create page
                result_pages := result_pages || jsonb_build_array(
                    jsonb_build_object(
                        'carriers', ordered_carriers_array,
                        'sections', current_page_sections
                    )
                );

                -- Reset for new page
                current_page_sections := '[]'::jsonb;
                current_section_benefits := '[]'::jsonb;
                current_page_benefits := 0;
            END IF;

            -- Add benefit to current section
            current_section_benefits := current_section_benefits || jsonb_build_array(benefit_obj);
            current_page_benefits := current_page_benefits + 1;
        END LOOP;
    END LOOP;

    -- Finish last section
    IF current_section_name != '' AND jsonb_array_length(current_section_benefits) > 0 THEN
        current_page_sections := current_page_sections || jsonb_build_array(
            jsonb_build_object(
                'name', current_section_display_name,
                'id', current_section_id,
                'benefits', current_section_benefits
            )
        );
    END IF;

    -- Add final page
    IF jsonb_array_length(current_page_sections) > 0 THEN
        result_pages := result_pages || jsonb_build_array(
            jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', current_page_sections
            )
        );
    END IF;

    -- Return array of paginated results
    RETURN result_pages::text;
END;
$$;
        ]]>
        </sql>