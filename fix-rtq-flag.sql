-- <PERSON><PERSON>t to fix the RTQ flag issue
-- This will set rtq_set_flag = TRUE for the first employee class that has quotes

DO $$
DECLARE
    target_plan_uuid TEXT := '6bfca487-5e29-4eab-85c4-8626a5810142';
    target_employee_class_id INT;
    plan_id_val INT;
    quote_count INT;
BEGIN
    -- Get the plan_id
    SELECT p.plan_id INTO plan_id_val
    FROM sandf.plan p 
    WHERE p.plan_uuid = target_plan_uuid::uuid;
    
    IF plan_id_val IS NULL THEN
        RAISE NOTICE 'Plan not found for UUID: %', target_plan_uuid;
        RETURN;
    END IF;
    
    RAISE NOTICE 'Found plan_id: % for UUID: %', plan_id_val, target_plan_uuid;
    
    -- Find the first employee class that has quotes
    SELECT ec.employee_class_id INTO target_employee_class_id
    FROM sandf.employee_class ec
    WHERE ec.plan_id = plan_id_val
    AND EXISTS (
        SELECT 1 
        FROM sandf.employee_class_quote ecq 
        WHERE ecq.employee_class_id = ec.employee_class_id
    )
    ORDER BY ec.employee_class_id ASC
    LIMIT 1;
    
    IF target_employee_class_id IS NULL THEN
        RAISE NOTICE 'No employee class with quotes found for plan_id: %', plan_id_val;
        RETURN;
    END IF;
    
    -- Check how many quotes this employee class has
    SELECT COUNT(*) INTO quote_count
    FROM sandf.employee_class_quote ecq
    WHERE ecq.employee_class_id = target_employee_class_id;
    
    RAISE NOTICE 'Found employee_class_id: % with % quotes', target_employee_class_id, quote_count;
    
    -- Update the rtq_set_flag
    UPDATE sandf.employee_class 
    SET rtq_set_flag = TRUE,
        updated_date = CURRENT_TIMESTAMP
    WHERE employee_class_id = target_employee_class_id;
    
    RAISE NOTICE 'Updated employee_class_id: % to have rtq_set_flag = TRUE', target_employee_class_id;
    
    -- Verify the update
    SELECT ec.employee_class_id, ec.name, ec.rtq_set_flag
    FROM sandf.employee_class ec
    WHERE ec.employee_class_id = target_employee_class_id;

    RAISE NOTICE 'Verification: employee_class_id % now has rtq_set_flag = TRUE', target_employee_class_id;

END $$;

-- Show the current state after the fix
SELECT
    ec.employee_class_id,
    ec.name as class_name,
    ec.rtq_set_flag,
    COUNT(ecq.employee_class_quote_id) as quote_count
FROM sandf.employee_class ec
JOIN sandf.plan p ON p.plan_id = ec.plan_id
LEFT JOIN sandf.employee_class_quote ecq ON ecq.employee_class_id = ec.employee_class_id
WHERE p.plan_uuid = '6bfca487-5e29-4eab-85c4-8626a5810142'
GROUP BY ec.employee_class_id, ec.name, ec.rtq_set_flag
ORDER BY ec.employee_class_id;
