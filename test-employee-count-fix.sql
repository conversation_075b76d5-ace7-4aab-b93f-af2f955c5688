-- Test script to verify the employee count fix in multi-class rate-per-employee function
-- This test should show that each quote uses its own employee count for calculations

-- Test the multi-class rate per employee function
SELECT 'Testing multi-class rate per employee function with employee count fix' AS test_description;

-- The fix ensures that:
-- 1. Each quote calculates its own employee count using fn_get_no_of_employee_count
-- 2. Each quote uses its own employee count for shared cost division
-- 3. No accumulation or summing of employee counts across quotes
-- 4. Final calculations happen per quote, just like RTQ function
-- 5. Each quote's shared amount = quote's shared total ÷ quote's employee count

-- Replace with actual plan UUID from your test data
SELECT sandf.fn_get_rate_per_employee_multi_class(
    '123e4567-e89b-12d3-a456-************',  -- Replace with actual plan UUID
    'test_user',
    NULL,  -- includes_param
    NULL,  -- excludes_param
    NULL   -- includes_quotes_uuid
);
