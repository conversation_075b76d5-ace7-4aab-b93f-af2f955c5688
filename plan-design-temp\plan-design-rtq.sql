  <sql splitStatements="false">
            <![CDATA[
        CREATE OR REPLACE FUNCTION sandf.fn_get_plan_design_report_RTQ(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    plan_details_key TEXT := 'planDetails';
    rtq_name TEXT := 'RTQ';
    quote_record RECORD;
    plan_details JSONB;
    fields JSONB;
    field_item JSONB;
    carriers_array JSONB := '[]'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefits_array JSONB;
    benefit_obj JSONB;
    carrier_name TEXT;
    group_name TEXT;
    group_display_name TEXT;
    benefit_name TEXT;
    benefit_key TEXT;
    benefit_values JSONB := '{}'::jsonb;
    field_details JSONB;
    field_detail JSONB;
    carrier_value TEXT;
    section_map JSONB := '{}'::jsonb;
    section_names TEXT[];
    section_name TEXT;
    section_id TEXT;
    section_display_name TEXT;
    benefit_map JSONB := '{}'::jsonb;
    benefit_keys TEXT[];
    benefit_key_item TEXT;
    section_order_record RECORD;
    benefit_order_record RECORD;

    -- Variables for skip condition logic
    skip_fields TEXT[] := ARRAY['maximumLife', 'maximumADAD'];
    coverage_life_values JSONB := '{}'::jsonb;
    field_values_to_check JSONB := '{}'::jsonb;
    should_skip_field BOOLEAN;
    carrier_check TEXT;
    coverage_value TEXT;
    field_value TEXT;

    -- Variables for carrier ordering
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order INTEGER;
    quote_uuid_val UUID;

    -- Pagination variables
    MAX_BENEFITS_PER_PAGE INTEGER := 18; -- Updated to 14 lines per page including section names
    all_benefits JSONB := '[]'::jsonb;
    all_sections JSONB := '[]'::jsonb;
    current_page_benefits INTEGER := 0;
    current_page_sections JSONB := '[]'::jsonb;
    current_section_benefits JSONB := '[]'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    total_benefits INTEGER;
    total_sections INTEGER;
    current_section_name TEXT;
    current_section_id TEXT;
    current_section_display_name TEXT;
    section_idx INTEGER;
    benefit_idx INTEGER;
    section_benefit_count INTEGER;

    -- Fixed pagination configuration (3 objects)
    pagination_config JSONB;
    page_number INTEGER;

    -- Enhanced pagination variables
    available_sections_by_order JSONB := '{}'::jsonb;
    sections_without_order JSONB := '[]'::jsonb;
    current_page_items INTEGER := 0; -- Count both benefits and section headers
    use_fallback_pagination BOOLEAN := FALSE;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;

    -- Configuration variables for filtering and ordering
    config_plan_details JSONB;
    config_overview JSONB;
    config_section_key TEXT;
    config_section_info JSONB;
    config_display_items JSONB;
    mapped_section_key TEXT;
    carrier_has_data BOOLEAN;
    config_section_keys TEXT[];
    config_section_order INTEGER;
    config_json JSONB;

BEGIN
    -- Load configuration from config.json_storage table
    SELECT json_data INTO config_json
    FROM config.json_storage
    WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
    LIMIT 1;

    -- Extract planDetails from the configuration
    config_plan_details := config_json -> 'planDetails';

    -- Extract pagination configuration from the configuration
    pagination_config := config_json -> 'paginationRTQ';
    IF pagination_config IS NULL THEN
        -- Default pagination configuration if not found in config
        pagination_config := '[
            [1, 2, 3],
            [4, 5, 6, 7],
            [8, 9]
        ]'::jsonb;
    END IF;

    -- Get the order of sections from config_plan_details keys (maintains order from config.json)
    SELECT array_agg(key ORDER BY ordinality) INTO config_section_keys
    FROM jsonb_each(config_plan_details) WITH ORDINALITY;

    -- First pass: Build carrier order map and collect all carriers
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = 'RTQ'
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
    LOOP
        carrier_name := quote_record.carrier_description;

        -- Use the new function to get carrier order
        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999  -- default order
        );

        RAISE NOTICE 'Carrier: %, Quote ID: %, Order: %',
                     carrier_name, quote_record.quote_id, carrier_order;

        -- Build carrier order map using carrier name as key
        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order,
                    'quote_id', quote_record.quote_id,
                    'quote_uuid', quote_record.quote_uuid
                )
            );
        END IF;
    END LOOP;

    -- Set the first carrier name for display purposes (before processing benefits)
    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
        LIMIT 1
    LOOP
        first_carrier_name := carrier_item;
    END LOOP;

    -- Second pass: Process quotes to build plan data with proper ordering
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = 'RTQ'
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
        -- Order using the new function
        ORDER BY
            sandf.get_user_preference_order(
                user_id_param,
                plan_uuid_param,
                q.quote_id,
                q.quote_uuid,
                999999
            ) ASC,
            c.description ASC
    LOOP
        carrier_name := quote_record.carrier_description;

        -- Continue with existing logic for processing plan details
        plan_details := quote_record.formatted_quote_details -> plan_details_key;

        IF plan_details IS NOT NULL AND jsonb_typeof(plan_details) = 'object' THEN
            fields := plan_details -> 'fields';
            IF fields IS NOT NULL AND jsonb_typeof(fields) = 'array' THEN
                FOR field_item IN SELECT jsonb_array_elements(fields)
                LOOP
                    group_name := field_item ->> 'groupName';
                    IF group_name IS NULL THEN
                        group_name := field_item ->> 'name';
                    END IF;

                    -- Check if this section is in our configuration
                    mapped_section_key := NULL;
                    config_section_info := NULL;

                    -- Map lifeInsuranceADAD to both lifeInsurance and ADAD sections
                    IF group_name = 'lifeInsuranceADAD' THEN
                        -- Process as lifeInsurance first
                        IF config_plan_details ? 'lifeInsurance' THEN
                            mapped_section_key := 'lifeInsurance';
                            config_section_info := config_plan_details -> 'lifeInsurance';
                        END IF;
                    ELSIF config_plan_details ? group_name THEN
                        mapped_section_key := group_name;
                        config_section_info := config_plan_details -> group_name;
                    END IF;

                    -- Only process if section is in configuration
                    IF config_section_info IS NOT NULL AND
                       (includes_param IS NULL OR group_name = ANY(includes_param)) AND
                       (excludes_param IS NULL OR NOT (group_name = ANY(excludes_param))) THEN

                        group_display_name := config_section_info ->> mapped_section_key;
                        config_display_items := config_section_info -> 'displayItems';

                        section_map := section_map || jsonb_build_object(mapped_section_key, group_display_name);

                        field_details := field_item -> 'fields';
                        IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                            FOR field_detail IN SELECT jsonb_array_elements(field_details)
                            LOOP
                                benefit_key := field_detail ->> 'name';
                                carrier_value := field_detail ->> 'value';

                                -- Only process fields that are in displayItems configuration
                                IF config_display_items ? benefit_key THEN
                                    benefit_name := config_display_items ->> benefit_key;

                                    -- Skip processing if value is null, empty, or whitespace only
                                    IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                        CONTINUE; -- Skip this field entirely if no value
                                    ELSE
                                        -- Validate as numeric if appropriate
                                        BEGIN
                                            PERFORM sandf.safe_parse_numeric(carrier_value);
                                        EXCEPTION WHEN OTHERS THEN
                                            -- If validation fails, skip this field
                                            CONTINUE;
                                        END;
                                    END IF;

                                    -- Use "Current" for first carrier, otherwise use actual name
                                    IF carrier_name = first_carrier_name THEN
                                        display_carrier_name := 'Current';
                                    ELSE
                                        display_carrier_name := carrier_name;
                                    END IF;

                                    -- Store coverageLife values for comparison
                                    IF benefit_key = 'coverageLife' THEN
                                        coverage_life_values := coverage_life_values || jsonb_build_object(display_carrier_name, carrier_value);
                                    END IF;

                                    -- Store values for fields that need to be checked against coverageLife
                                    IF benefit_key = ANY(skip_fields) THEN
                                        IF NOT field_values_to_check ? benefit_key THEN
                                            field_values_to_check := field_values_to_check || jsonb_build_object(benefit_key, '{}'::jsonb);
                                        END IF;
                                        field_values_to_check := jsonb_set(
                                            field_values_to_check,
                                            ARRAY[benefit_key, display_carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;

                                    benefit_key_item := mapped_section_key || '.' || benefit_key;

                                    IF NOT benefit_map ? benefit_key_item THEN
                                        benefit_map := benefit_map || jsonb_build_object(
                                            benefit_key_item,
                                            jsonb_build_object(
                                                'name', benefit_name,
                                                'key', benefit_key,
                                                'section', mapped_section_key,
                                                'values', '{}'::jsonb
                                            )
                                        );
                                    END IF;

                                    benefit_map := jsonb_set(
                                        benefit_map,
                                        ARRAY[benefit_key_item, 'values', display_carrier_name],
                                        to_jsonb(carrier_value)
                                    );
                                END IF;
                            END LOOP;
                        END IF;

                        -- Handle ADAD section separately for lifeInsuranceADAD
                        IF group_name = 'lifeInsuranceADAD' AND config_plan_details ? 'ADAD' THEN
                            config_section_info := config_plan_details -> 'ADAD';
                            group_display_name := config_section_info ->> 'ADAD';
                            config_display_items := config_section_info -> 'displayItems';

                            -- Add ADAD section mapping
                            section_map := section_map || jsonb_build_object('ADAD', group_display_name);

                            -- Process ADAD fields
                            field_details := field_item -> 'fields';
                            IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                                FOR field_detail IN SELECT jsonb_array_elements(field_details)
                                LOOP
                                    benefit_key := field_detail ->> 'name';
                                    carrier_value := field_detail ->> 'value';

                                    -- Only process ADAD fields that are in displayItems configuration
                                    IF config_display_items ? benefit_key THEN
                                        benefit_name := config_display_items ->> benefit_key;

                                        -- Skip processing if value is null, empty, or whitespace only
                                        IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                            CONTINUE; -- Skip this field entirely if no value
                                        ELSE
                                            BEGIN
                                                PERFORM sandf.safe_parse_numeric(carrier_value);
                                            EXCEPTION WHEN OTHERS THEN
                                                -- If validation fails, skip this field
                                                CONTINUE;
                                            END;
                                        END IF;

                                        -- Use "Current" for first carrier, otherwise use actual name
                                        IF carrier_name = first_carrier_name THEN
                                            display_carrier_name := 'Current';
                                        ELSE
                                            display_carrier_name := carrier_name;
                                        END IF;

                                        -- Store values for fields that need to be checked against coverageLife
                                        IF benefit_key = ANY(skip_fields) THEN
                                            IF NOT field_values_to_check ? benefit_key THEN
                                                field_values_to_check := field_values_to_check || jsonb_build_object(benefit_key, '{}'::jsonb);
                                            END IF;
                                            field_values_to_check := jsonb_set(
                                                field_values_to_check,
                                                ARRAY[benefit_key, display_carrier_name],
                                                to_jsonb(carrier_value)
                                            );
                                        END IF;

                                        benefit_key_item := 'ADAD' || '.' || benefit_key;

                                        IF NOT benefit_map ? benefit_key_item THEN
                                            benefit_map := benefit_map || jsonb_build_object(
                                                benefit_key_item,
                                                jsonb_build_object(
                                                    'name', benefit_name,
                                                    'key', benefit_key,
                                                    'section', 'ADAD',
                                                    'values', '{}'::jsonb
                                                )
                                            );
                                        END IF;

                                        benefit_map := jsonb_set(
                                            benefit_map,
                                            ARRAY[benefit_key_item, 'values', display_carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;
                                END LOOP;
                            END IF;
                        END IF;
                    END IF;
                END LOOP;
            ELSE
                -- Fallback to simple key-value object (existing logic)
                DECLARE
                    benefit_data JSONB;
                    field_key TEXT;
                    show_value TEXT;
                BEGIN
                    FOR benefit_key IN SELECT jsonb_object_keys(plan_details)
                    LOOP
                        -- Check if this section is in our configuration
                        mapped_section_key := NULL;
                        config_section_info := NULL;

                        -- Map lifeInsuranceADAD to both lifeInsurance and ADAD sections
                        IF benefit_key = 'lifeInsuranceADAD' THEN
                            -- Process as lifeInsurance first
                            IF config_plan_details ? 'lifeInsurance' THEN
                                mapped_section_key := 'lifeInsurance';
                                config_section_info := config_plan_details -> 'lifeInsurance';
                            END IF;
                        ELSIF config_plan_details ? benefit_key THEN
                            mapped_section_key := benefit_key;
                            config_section_info := config_plan_details -> benefit_key;
                        END IF;

                        -- Only process if section is in configuration
                        IF config_section_info IS NOT NULL AND
                           (includes_param IS NULL OR benefit_key = ANY(includes_param)) AND
                           (excludes_param IS NULL OR NOT (benefit_key = ANY(excludes_param))) THEN

                            benefit_data := plan_details -> benefit_key;

                            IF benefit_data IS NULL OR jsonb_typeof(benefit_data) != 'object' THEN
                                CONTINUE;
                            END IF;

                            show_value := benefit_data ->> 'show';
                            IF show_value IS NOT NULL AND show_value::boolean = false THEN
                                CONTINUE;
                            END IF;

                            section_display_name := config_section_info ->> mapped_section_key;
                            config_display_items := config_section_info -> 'displayItems';

                            section_map := section_map || jsonb_build_object(mapped_section_key, section_display_name);

                            FOR field_key IN SELECT jsonb_object_keys(benefit_data)
                            LOOP
                                IF field_key = 'show' THEN
                                    CONTINUE;
                                END IF;

                                -- Only process fields that are in displayItems configuration
                                IF config_display_items ? field_key THEN
                                    benefit_name := config_display_items ->> field_key;

                                    carrier_value := benefit_data ->> field_key;
                                    -- Skip processing if value is null, empty, or whitespace only
                                    IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                        CONTINUE; -- Skip this field entirely if no value
                                    ELSE
                                        -- Validate as numeric if appropriate
                                        BEGIN
                                            PERFORM sandf.safe_parse_numeric(carrier_value);
                                        EXCEPTION WHEN OTHERS THEN
                                            -- If validation fails, skip this field
                                            CONTINUE;
                                        END;
                                    END IF;

                                    -- Use "Current" for first carrier, otherwise use actual name
                                    IF carrier_name = first_carrier_name THEN
                                        display_carrier_name := 'Current';
                                    ELSE
                                        display_carrier_name := carrier_name;
                                    END IF;

                                    -- Store coverageLife values for comparison (fallback section)
                                    IF field_key = 'coverageLife' THEN
                                        coverage_life_values := coverage_life_values || jsonb_build_object(display_carrier_name, carrier_value);
                                    END IF;

                                    -- Store values for fields that need to be checked against coverageLife (fallback section)
                                    IF field_key = ANY(skip_fields) THEN
                                        IF NOT field_values_to_check ? field_key THEN
                                            field_values_to_check := field_values_to_check || jsonb_build_object(field_key, '{}'::jsonb);
                                        END IF;
                                        field_values_to_check := jsonb_set(
                                            field_values_to_check,
                                            ARRAY[field_key, display_carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;

                                    benefit_key_item := mapped_section_key || '.' || field_key;
                                    IF NOT benefit_map ? benefit_key_item THEN
                                        benefit_map := benefit_map || jsonb_build_object(
                                            benefit_key_item,
                                            jsonb_build_object(
                                                'name', benefit_name,
                                                'key', field_key,
                                                'section', mapped_section_key,
                                                'values', '{}'::jsonb
                                            )
                                        );
                                    END IF;

                                    benefit_map := jsonb_set(
                                        benefit_map,
                                        ARRAY[benefit_key_item, 'values', display_carrier_name],
                                        to_jsonb(carrier_value)
                                    );
                                END IF;
                            END LOOP;

                            -- Handle ADAD section separately for lifeInsuranceADAD
                            IF benefit_key = 'lifeInsuranceADAD' AND config_plan_details ? 'ADAD' THEN
                                config_section_info := config_plan_details -> 'ADAD';
                                section_display_name := config_section_info ->> 'ADAD';
                                config_display_items := config_section_info -> 'displayItems';

                                -- Add ADAD section mapping
                                section_map := section_map || jsonb_build_object('ADAD', section_display_name);

                                -- Process ADAD fields
                                FOR field_key IN SELECT jsonb_object_keys(benefit_data)
                                LOOP
                                    IF field_key = 'show' THEN
                                        CONTINUE;
                                    END IF;

                                    -- Only process ADAD fields that are in displayItems configuration
                                    IF config_display_items ? field_key THEN
                                        benefit_name := config_display_items ->> field_key;

                                        carrier_value := benefit_data ->> field_key;
                                        -- Skip processing if value is null, empty, or whitespace only
                                        IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                            CONTINUE; -- Skip this field entirely if no value
                                        ELSE
                                            BEGIN
                                                PERFORM sandf.safe_parse_numeric(carrier_value);
                                            EXCEPTION WHEN OTHERS THEN
                                                -- If validation fails, skip this field
                                                CONTINUE;
                                            END;
                                        END IF;

                                        -- Use "Current" for first carrier, otherwise use actual name
                                        IF carrier_name = first_carrier_name THEN
                                            display_carrier_name := 'Current';
                                        ELSE
                                            display_carrier_name := carrier_name;
                                        END IF;

                                        -- Store values for fields that need to be checked against coverageLife
                                        IF field_key = ANY(skip_fields) THEN
                                            IF NOT field_values_to_check ? field_key THEN
                                                field_values_to_check := field_values_to_check || jsonb_build_object(field_key, '{}'::jsonb);
                                            END IF;
                                            field_values_to_check := jsonb_set(
                                                field_values_to_check,
                                                ARRAY[field_key, display_carrier_name],
                                                to_jsonb(carrier_value)
                                            );
                                        END IF;

                                        benefit_key_item := 'ADAD' || '.' || field_key;

                                        IF NOT benefit_map ? benefit_key_item THEN
                                            benefit_map := benefit_map || jsonb_build_object(
                                                benefit_key_item,
                                                jsonb_build_object(
                                                    'name', benefit_name,
                                                    'key', field_key,
                                                    'section', 'ADAD',
                                                    'values', '{}'::jsonb
                                                )
                                            );
                                        END IF;

                                        benefit_map := jsonb_set(
                                            benefit_map,
                                            ARRAY[benefit_key_item, 'values', display_carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;
                                END LOOP;
                            END IF;
                        END IF;
                    END LOOP;
                END;
            END IF;
        END IF;
    END LOOP;

    -- Order carriers based on user preferences using the carrier_order_map
    RAISE NOTICE 'Final carrier_order_map: %', carrier_order_map;

    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
    LOOP
        -- Use "Current" for the first carrier, otherwise use actual name
        IF carrier_item = first_carrier_name THEN
            display_carrier_name := 'Current';
        ELSE
            display_carrier_name := carrier_item;
        END IF;

        RAISE NOTICE 'Adding carrier to ordered array: % with order: %',
                     carrier_item, (carrier_order_map -> carrier_item ->> 'order');
        ordered_carriers_array := ordered_carriers_array || jsonb_build_array(display_carrier_name);
    END LOOP;

    RAISE NOTICE 'Final ordered carriers: %', ordered_carriers_array;

    -- First, build all sections normally (with skip logic applied)
    FOR section_order_record IN
        SELECT
            key as section_name,
            COALESCE(
                (config_plan_details -> key ->> 'order')::integer,
                999999
            ) as sort_order
        FROM jsonb_each_text(section_map)
        ORDER BY sort_order ASC, key ASC
    LOOP
        section_name := section_order_record.section_name;
        section_id := lower(replace(section_name, ' ', ''));
        section_display_name := COALESCE(section_map ->> section_name, section_name);
        benefits_array := '[]'::jsonb;

        -- Order benefits within each section by display_order
        FOR benefit_order_record IN
            SELECT
                key as benefit_key_item,
                value as benefit_obj,
                COALESCE(uf.display_order, 999999) as sort_order
            FROM jsonb_each(benefit_map)
            LEFT JOIN sandf.ui_field uf ON uf.name = (value ->> 'key')
            WHERE (value ->> 'section') = section_name
            ORDER BY sort_order ASC, (value ->> 'key') ASC
        LOOP
            benefit_obj := benefit_order_record.benefit_obj;

            -- Apply skip condition: Check if this field should be skipped
            should_skip_field := FALSE;

            -- Check if this is one of the fields to be compared with coverageLife
            IF (benefit_obj ->> 'key') = ANY(skip_fields) THEN
                should_skip_field := TRUE;

                -- Check if all carriers have the same value for this field AND coverageLife
                FOR carrier_check IN SELECT jsonb_object_keys(coverage_life_values)
                LOOP
                    coverage_value := coverage_life_values ->> carrier_check;
                    field_value := (field_values_to_check -> (benefit_obj ->> 'key')) ->> carrier_check;

                    -- If any carrier has different values, don't skip
                    IF coverage_value != field_value THEN
                        should_skip_field := FALSE;
                        EXIT;
                    END IF;
                END LOOP;
            END IF;

            -- Check if benefit has any non-empty values across all carriers
            IF NOT should_skip_field THEN
                carrier_has_data := FALSE;
                FOR carrier_item IN SELECT jsonb_object_keys(benefit_obj -> 'values')
                LOOP
                    carrier_value := benefit_obj -> 'values' ->> carrier_item;
                    IF carrier_value IS NOT NULL AND trim(carrier_value) != '' AND carrier_value != '-' THEN
                        carrier_has_data := TRUE;
                        EXIT;
                    END IF;
                END LOOP;

                -- Only add benefit if it has data and shouldn't be skipped
                IF carrier_has_data THEN
                    benefits_array := benefits_array || jsonb_build_array(benefit_obj);
                END IF;
            END IF;
        END LOOP;

        -- Only add section if it has benefits
        IF jsonb_array_length(benefits_array) > 0 THEN
            section_obj := jsonb_build_object(
                'name', section_display_name,
                'id', section_id,
                'benefits', benefits_array
            );

            all_sections := all_sections || jsonb_build_array(section_obj);
        END IF;
    END LOOP;

    -- Enhanced pagination: Handle missing order data and use intelligent fallback
    --
    -- Key Requirements:
    -- 1. NEVER BREAK SECTIONS - Each section must be kept complete within one page
    -- 2. Handles cases where order-based sections are missing or empty
    -- 3. Uses length-based pagination as fallback when insufficient order data
    -- 4. Dynamic page creation (1, 2, or 3 pages) - never exceeds 3 pages, but creates only what's needed
    -- 5. Shows up to 18 items per page (including section headers)
    -- 6. Intelligently redistributes content when order data is incomplete
    -- 7. Section integrity is prioritized over strict page limits
    -- 8. Empty pages are not created - only pages with actual content
    --
    result_pages := '[]'::jsonb;

    -- Step 1: Categorize sections by their order and identify missing orders
    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        section_obj := all_sections -> section_idx;
        section_name := section_obj ->> 'name';

        -- Find the section key and its order
        DECLARE
            section_key TEXT;
            section_order INTEGER;
            found_order BOOLEAN := FALSE;
        BEGIN
            FOR section_key IN SELECT jsonb_object_keys(section_map) LOOP
                IF section_map ->> section_key = section_name THEN
                    section_order := COALESCE(
                        (config_plan_details -> section_key ->> 'order')::integer,
                        999999
                    );

                    -- Group sections by their order
                    IF section_order != 999999 THEN
                        IF NOT available_sections_by_order ? section_order::text THEN
                            available_sections_by_order := available_sections_by_order ||
                                jsonb_build_object(section_order::text, '[]'::jsonb);
                        END IF;
                        available_sections_by_order := jsonb_set(
                            available_sections_by_order,
                            ARRAY[section_order::text],
                            (available_sections_by_order -> section_order::text) || jsonb_build_array(section_obj)
                        );
                        found_order := TRUE;
                    END IF;
                    EXIT;
                END IF;
            END LOOP;

            -- If no order found, add to sections without order
            IF NOT found_order THEN
                sections_without_order := sections_without_order || jsonb_build_array(section_obj);
            END IF;
        END;
    END LOOP;

    -- Step 2: Check if we have sufficient order-based data for 3 pages
    DECLARE
        orders_with_data INTEGER := 0;
        total_ordered_items INTEGER := 0;
        order_key TEXT;
        sections_in_order JSONB;
    BEGIN
        -- Count orders that have data and total items
        FOR order_key IN SELECT jsonb_object_keys(available_sections_by_order) LOOP
            sections_in_order := available_sections_by_order -> order_key;
            IF jsonb_array_length(sections_in_order) > 0 THEN
                orders_with_data := orders_with_data + 1;
                -- Count benefits + section headers for this order
                FOR section_idx IN 0..jsonb_array_length(sections_in_order)-1 LOOP
                    total_ordered_items := total_ordered_items + 1 + -- section header
                        jsonb_array_length(sections_in_order -> section_idx -> 'benefits');
                END LOOP;
            END IF;
        END LOOP;

        -- Add items from sections without order
        FOR section_idx IN 0..jsonb_array_length(sections_without_order)-1 LOOP
            total_ordered_items := total_ordered_items + 1 + -- section header
                jsonb_array_length(sections_without_order -> section_idx -> 'benefits');
        END LOOP;

        -- Use fallback if we don't have enough order-based data or too few orders
        IF orders_with_data < 2 OR total_ordered_items < (MAX_BENEFITS_PER_PAGE * 2) THEN
            use_fallback_pagination := TRUE;
        END IF;
    END;

    -- Step 3: Apply appropriate pagination strategy
    IF use_fallback_pagination THEN
        -- Fallback: Length-based pagination ensuring max 3 pages
        RAISE NOTICE 'Using fallback length-based pagination';

        current_page_sections := '[]'::jsonb;
        current_page_items := 0;

        -- Process all sections in order, then sections without order
        -- IMPORTANT: Never break sections - keep each section complete within one page
        FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
            section_obj := all_sections -> section_idx;
            section_benefit_count := jsonb_array_length(section_obj -> 'benefits');

            -- Check if adding this COMPLETE section would exceed page limit
            IF current_page_items > 0 AND (current_page_items + 1 + section_benefit_count) > MAX_BENEFITS_PER_PAGE THEN
                -- Start new page if we haven't reached 3 pages yet
                IF jsonb_array_length(result_pages) < 2 THEN
                    result_pages := result_pages || jsonb_build_array(
                        jsonb_build_object(
                            'carriers', ordered_carriers_array,
                            'sections', current_page_sections
                        )
                    );
                    current_page_sections := '[]'::jsonb;
                    current_page_items := 0;
                ELSE
                    -- If we're at page limit, add section to current page anyway to maintain section integrity
                    -- This ensures no section is broken across pages
                    RAISE NOTICE 'Adding section to final page to maintain section integrity: %', section_obj ->> 'name';
                END IF;
            END IF;

            -- Add COMPLETE section to current page (never break sections)
            current_page_sections := current_page_sections || jsonb_build_array(section_obj);
            current_page_items := current_page_items + 1 + section_benefit_count; -- +1 for section header
        END LOOP;

        -- Add final page only if it has content
        IF jsonb_array_length(current_page_sections) > 0 THEN
            result_pages := result_pages || jsonb_build_array(
                jsonb_build_object(
                    'carriers', ordered_carriers_array,
                    'sections', current_page_sections
                )
            );
        END IF;

        -- Dynamic page creation: Only create the pages needed (1, 2, or 3)
        -- No need to force empty pages - return actual content pages only

    ELSE
        -- Order-based pagination with intelligent redistribution
        RAISE NOTICE 'Using order-based pagination with redistribution';

        -- Loop through each page configuration (up to 3 pages, but create only what's needed)
        FOR page_number IN 0..jsonb_array_length(pagination_config)-1 LOOP
            current_page_sections := '[]'::jsonb;
            current_page_items := 0;

            -- Get the order array for this page
            DECLARE
                page_orders JSONB := pagination_config -> page_number;
                order_idx INTEGER;
                target_order INTEGER;
                sections_for_order JSONB;
            BEGIN
                -- Loop through each order number for this page
                FOR order_idx IN 0..jsonb_array_length(page_orders)-1 LOOP
                    target_order := (page_orders -> order_idx)::integer;

                    -- Get sections for this order if they exist
                    IF available_sections_by_order ? target_order::text THEN
                        sections_for_order := available_sections_by_order -> target_order::text;

                        -- Add COMPLETE sections from this order to current page
                        -- IMPORTANT: Never break sections - only add if entire section fits
                        FOR section_idx IN 0..jsonb_array_length(sections_for_order)-1 LOOP
                            section_obj := sections_for_order -> section_idx;
                            section_benefit_count := jsonb_array_length(section_obj -> 'benefits');

                            -- Only add section if COMPLETE section fits within page capacity
                            IF current_page_items + 1 + section_benefit_count <= MAX_BENEFITS_PER_PAGE THEN
                                current_page_sections := current_page_sections || jsonb_build_array(section_obj);
                                current_page_items := current_page_items + 1 + section_benefit_count;
                            ELSE
                                -- Section doesn't fit completely, skip to maintain section integrity
                                RAISE NOTICE 'Skipping section % on page % to maintain section integrity (would exceed capacity)',
                                           section_obj ->> 'name', page_number + 1;
                            END IF;
                        END LOOP;
                    END IF;
                END LOOP;

                -- If page is still under capacity, add COMPLETE sections without order
                -- IMPORTANT: Only add sections that fit completely to maintain section integrity
                IF current_page_items < MAX_BENEFITS_PER_PAGE AND jsonb_array_length(sections_without_order) > 0 THEN
                    FOR section_idx IN 0..jsonb_array_length(sections_without_order)-1 LOOP
                        section_obj := sections_without_order -> section_idx;
                        section_benefit_count := jsonb_array_length(section_obj -> 'benefits');

                        -- Only add if COMPLETE section fits within remaining page capacity
                        IF current_page_items + 1 + section_benefit_count <= MAX_BENEFITS_PER_PAGE THEN
                            current_page_sections := current_page_sections || jsonb_build_array(section_obj);
                            current_page_items := current_page_items + 1 + section_benefit_count;

                            -- Remove this section from sections_without_order to avoid duplication
                            sections_without_order := (
                                SELECT jsonb_agg(elem)
                                FROM (
                                    SELECT elem, row_number() OVER () as rn
                                    FROM jsonb_array_elements(sections_without_order) elem
                                ) t
                                WHERE rn != section_idx + 1
                            );
                            EXIT; -- Only add one section per page to maintain balance
                        ELSE
                            -- Section doesn't fit completely, skip to maintain section integrity
                            RAISE NOTICE 'Skipping section % on page % - insufficient space for complete section',
                                       section_obj ->> 'name', page_number + 1;
                        END IF;
                    END LOOP;
                END IF;
            END;

            -- Add this page to results only if it has content
            IF jsonb_array_length(current_page_sections) > 0 THEN
                result_pages := result_pages || jsonb_build_array(
                    jsonb_build_object(
                        'carriers', ordered_carriers_array,
                        'sections', current_page_sections
                    )
                );
            END IF;
        END LOOP;
    END IF;

    -- Return array of dynamic paginated results (1, 2, or 3 pages based on content)
    RETURN result_pages::text;
END;
$$;
        ]]>
        </sql>