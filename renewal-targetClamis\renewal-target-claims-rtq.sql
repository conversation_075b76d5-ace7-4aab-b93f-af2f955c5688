   <sql splitStatements="false">
            <![CDATA[
        CREATE OR REPLACE FUNCTION sandf.fn_get_renewal_charges_and_targeted_claims_RTQ(
    target_plan_uuid TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$

DECLARE
    v_plan_id INTEGER;
    v_employee_class_id INTEGER;
    quote_record RECORD;
    quote_json JSONB;
    carrier_name TEXT;
    carrier_list TEXT[] := '{}';
    section_list JSONB := '[]'::JSONB;
    renewal_section_list JSONB := '[]'::JSONB;

    -- Premium and volume variables
    ehc_prem NUMERIC := 0;
    ehc_vol NUMERIC := 0;
    dental_prem NUMERIC := 0;
    dental_vol NUMERIC := 0;
    ehc_avg NUMERIC := 0;
    dental_avg NUMERIC := 0;

    -- Rating factor variables
    trend_ehc NUMERIC := 0;
    ibnr_ehc NUMERIC := 0;
    tlr_ehc NUMERIC := 0;
    ibnr_dental NUMERIC := 0;
    tlr_dental NUMERIC := 0;
    amount NUMERIC := 0;
    renewal_percent NUMERIC := 0;

    -- Text variables for display
    trend_ehc_text TEXT;
    ibnr_ehc_text TEXT;
    tlr_ehc_text TEXT;
    ibnr_dental_text TEXT;
    tlr_dental_text TEXT;
    amount_text TEXT;
    renewal_percent_str TEXT;
    x RECORD;

    -- Helper function for safe numeric conversion
    safe_numeric_value NUMERIC;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;
BEGIN
    -- Get plan_id
    SELECT p.plan_id INTO v_plan_id
    FROM sandf.plan p
    WHERE p.plan_uuid = target_plan_uuid::uuid
    ORDER BY p.plan_id ASC
    LIMIT 1;

    IF v_plan_id IS NULL THEN
        RETURN jsonb_build_object('carriers', ARRAY[]::TEXT[], 'sections', '[]'::JSONB);
    END IF;

    -- Get first RTQ employee_class_id
    SELECT employee_class_id INTO v_employee_class_id
    FROM sandf.employee_class
    WHERE plan_id = v_plan_id 
    ORDER BY employee_class_id ASC
    LIMIT 1;

    IF v_employee_class_id IS NULL THEN
        RETURN jsonb_build_object('carriers', ARRAY[]::TEXT[], 'sections', '[]'::JSONB);
    END IF;

    -- Process quotes in order of user preference
    FOR quote_record IN
        SELECT ecq.*, q.quote_id, q.quote_uuid, c.description as carrier_name
        FROM sandf.employee_class_quote ecq
        JOIN sandf.quote q ON ecq.quote_id = q.quote_id
        JOIN sandf.carrier c ON q.carrier_id = c.carrier_id
        WHERE ecq.employee_class_id = v_employee_class_id
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
        ORDER BY
            sandf.get_user_preference_order(
                user_id_param,
                target_plan_uuid,
                q.quote_id,
                q.quote_uuid,
                999999
            ) ASC,
            c.description ASC
    LOOP
        quote_json := quote_record.formatted_quote_details::jsonb;
        carrier_name := quote_record.carrier_name;

        -- Capture the first carrier name for display purposes
        IF first_carrier_name IS NULL THEN
            first_carrier_name := carrier_name;
        END IF;

        -- Use "Current" for the first carrier, otherwise use actual name
        IF carrier_name = first_carrier_name THEN
            display_carrier_name := 'Current';
        ELSE
            display_carrier_name := carrier_name;
        END IF;

        carrier_list := array_append(carrier_list, display_carrier_name);

        -- Reset totals for each carrier
        ehc_prem := 0;
        ehc_vol := 0;
        dental_prem := 0;
        dental_vol := 0;

        -- Extract rating factors with improved null/empty/zero handling
        trend_ehc_text := quote_json #>> '{benefitPremiums,ratingFactors,extendedHealthRatingFactors,pooling}';
        BEGIN
            trend_ehc := CASE
                WHEN trend_ehc_text IS NULL OR TRIM(trend_ehc_text) = '' OR TRIM(trend_ehc_text) = '0' THEN 0
                ELSE COALESCE((regexp_replace(TRIM(trend_ehc_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
            END;
        EXCEPTION
            WHEN OTHERS THEN
                trend_ehc := 0;
        END;

        ibnr_ehc_text := quote_json #>> '{benefitPremiums,ratingFactors,extendedHealthRatingFactors,ibnr}';
        BEGIN
            ibnr_ehc := CASE
                WHEN ibnr_ehc_text IS NULL OR TRIM(ibnr_ehc_text) = '' OR TRIM(ibnr_ehc_text) = '0' THEN 0
                ELSE COALESCE((regexp_replace(TRIM(ibnr_ehc_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
            END;
        EXCEPTION
            WHEN OTHERS THEN
                ibnr_ehc := 0;
        END;

        tlr_ehc_text := quote_json #>> '{benefitPremiums,ratingFactors,extendedHealthRatingFactors,targetLossRatio}';
        BEGIN
            tlr_ehc := CASE
                WHEN tlr_ehc_text IS NULL OR TRIM(tlr_ehc_text) = '' OR TRIM(tlr_ehc_text) = '0' THEN 0
                ELSE 100 - COALESCE((regexp_replace(TRIM(tlr_ehc_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
            END;
        EXCEPTION
            WHEN OTHERS THEN
                tlr_ehc := 0;
        END;

        ibnr_dental_text := quote_json #>> '{benefitPremiums,ratingFactors,dentalRatingFactors,ibnr}';
        BEGIN
            ibnr_dental := CASE
                WHEN ibnr_dental_text IS NULL OR TRIM(ibnr_dental_text) = '' OR TRIM(ibnr_dental_text) = '0' THEN 0
                ELSE COALESCE((regexp_replace(TRIM(ibnr_dental_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
            END;
        EXCEPTION
            WHEN OTHERS THEN
                ibnr_dental := 0;
        END;

        tlr_dental_text := quote_json #>> '{benefitPremiums,ratingFactors,dentalRatingFactors,targetLossRatio}';
        BEGIN
            tlr_dental := CASE
                WHEN tlr_dental_text IS NULL OR TRIM(tlr_dental_text) = '' OR TRIM(tlr_dental_text) = '0' THEN 0
                ELSE 100 - COALESCE((regexp_replace(TRIM(tlr_dental_text), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
            END;
        EXCEPTION
            WHEN OTHERS THEN
                tlr_dental := 0;
        END;

        RAISE NOTICE '=== CHECKING EHC COUPLE COVERAGE LOGIC ===';

        -- Apply special logic for extended health couple coverage using global function
        IF quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'couple' AND quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'family' THEN
            DECLARE
                updated_couple_data_rtc_rtq_ehc JSONB;
                couple_data_ehc JSONB;
                family_data_ehc JSONB;
            BEGIN
                couple_data_ehc := quote_json #> '{benefitPremiums,extendedHealthPremium,couple}';
                family_data_ehc := quote_json #> '{benefitPremiums,extendedHealthPremium,family}';

                RAISE NOTICE 'EHC Couple data BEFORE fn_apply_family_to_couple_coverage: %', couple_data_ehc;
                RAISE NOTICE 'EHC Family data available for copying: %', family_data_ehc;
                RAISE NOTICE 'Calling fn_apply_family_to_couple_coverage for extendedHealth...';
                RAISE NOTICE 'NOTE: This function only applies family values when couple volume=0 AND rate=0 AND premium=0';

                updated_couple_data_rtc_rtq_ehc := sandf.fn_apply_family_to_couple_coverage(
                    'extendedHealth',
                    couple_data_ehc,
                    family_data_ehc
                );

                RAISE NOTICE 'EHC Couple data AFTER fn_apply_family_to_couple_coverage: %', updated_couple_data_rtc_rtq_ehc;

                -- Update the couple data if it was changed
                IF updated_couple_data_rtc_rtq_ehc != quote_json #> '{benefitPremiums,extendedHealthPremium,couple}' THEN
                    quote_json := jsonb_set(quote_json, '{benefitPremiums,extendedHealthPremium,couple}', updated_couple_data_rtc_rtq_ehc);
                    RAISE NOTICE 'EHC COUPLE COVERAGE UPDATED: Family values were applied to couple because couple had volume=0 AND rate=0 AND premium=0';
                ELSE
                    RAISE NOTICE 'EHC COUPLE COVERAGE NOT CHANGED: Couple already had valid data (not all zeros), so family values were not applied';
                END IF;
            END;
        ELSE
            RAISE NOTICE 'EHC couple/family coverage check: couple exists=%, family exists=%',
                (quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'couple'),
                (quote_json #> '{benefitPremiums,extendedHealthPremium}' ? 'family');
        END IF;

        RAISE NOTICE '=== CALCULATING EHC VOLUMES AND PREMIUMS ===';
        RAISE NOTICE 'EHC Premium data structure: %', quote_json #> '{benefitPremiums,extendedHealthPremium}';
        RAISE NOTICE 'EHC Volume calculation starting with: ehc_vol = %, ehc_prem = %', ehc_vol, ehc_prem;

        -- Calculate EHC volumes and premiums with safe conversion
        FOR x IN SELECT * FROM jsonb_each(quote_json #> '{benefitPremiums,extendedHealthPremium}')
        LOOP
            RAISE NOTICE '--- Processing EHC coverage type: % ---', x.key;
            RAISE NOTICE 'Full coverage data: %', x.value;

            DECLARE
                volume_before NUMERIC := ehc_vol;
                premium_before NUMERIC := ehc_prem;
            BEGIN
                -- Volume processing
                BEGIN
                    safe_numeric_value := CASE
                        WHEN x.value ->> 'volume' IS NULL OR TRIM(x.value ->> 'volume') = '' THEN 0
                        ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'volume'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                    END;
                    RAISE NOTICE 'EHC % VOLUME PROCESSING:', x.key;
                    RAISE NOTICE '  Raw volume value: "%"', COALESCE(x.value ->> 'volume', 'NULL');
                    RAISE NOTICE '  Parsed volume value: %', safe_numeric_value;
                    RAISE NOTICE '  Volume before adding: %', volume_before;
                    ehc_vol := ehc_vol + safe_numeric_value;
                    RAISE NOTICE '  Volume after adding: % (added %)', ehc_vol, safe_numeric_value;
                    RAISE NOTICE '  Volume calculation: % + % = %', volume_before, safe_numeric_value, ehc_vol;
                EXCEPTION
                    WHEN OTHERS THEN
                        RAISE NOTICE 'ERROR parsing EHC % volume: %, using 0', x.key, SQLERRM;
                        ehc_vol := ehc_vol + 0;
                        RAISE NOTICE '  Volume after error (no change): %', ehc_vol;
                END;

                -- Premium processing
                BEGIN
                    safe_numeric_value := CASE
                        WHEN x.value ->> 'premium' IS NULL OR TRIM(x.value ->> 'premium') = '' THEN 0
                        ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'premium'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                    END;
                    RAISE NOTICE 'EHC % PREMIUM PROCESSING:', x.key;
                    RAISE NOTICE '  Raw premium value: "%"', COALESCE(x.value ->> 'premium', 'NULL');
                    RAISE NOTICE '  Parsed premium value: %', safe_numeric_value;
                    RAISE NOTICE '  Premium before adding: %', premium_before;
                    ehc_prem := ehc_prem + safe_numeric_value;
                    RAISE NOTICE '  Premium after adding: % (added %)', ehc_prem, safe_numeric_value;
                    RAISE NOTICE '  Premium calculation: % + % = %', premium_before, safe_numeric_value, ehc_prem;
                EXCEPTION
                    WHEN OTHERS THEN
                        RAISE NOTICE 'ERROR parsing EHC % premium: %, using 0', x.key, SQLERRM;
                        ehc_prem := ehc_prem + 0;
                        RAISE NOTICE '  Premium after error (no change): %', ehc_prem;
                END;
            END;

            RAISE NOTICE '--- End EHC % processing ---', x.key;
        END LOOP;

        RAISE NOTICE '=== EHC VOLUME/PREMIUM CALCULATION COMPLETE ===';
        RAISE NOTICE 'EHC FINAL TOTALS - Volume: %, Premium: %', ehc_vol, ehc_prem;

        RAISE NOTICE '=== CHECKING DENTAL COUPLE COVERAGE LOGIC ===';

        -- Apply special logic for dental couple coverage using global function
        IF quote_json #> '{benefitPremiums,dentalCarePremium}' ? 'couple' AND quote_json #> '{benefitPremiums,dentalCarePremium}' ? 'family' THEN
            DECLARE
                updated_couple_data_rtc_rtq_dental JSONB;
                couple_data_dental JSONB;
                family_data_dental JSONB;
            BEGIN
                couple_data_dental := quote_json #> '{benefitPremiums,dentalCarePremium,couple}';
                family_data_dental := quote_json #> '{benefitPremiums,dentalCarePremium,family}';

                RAISE NOTICE 'Dental Couple data BEFORE fn_apply_family_to_couple_coverage: %', couple_data_dental;
                RAISE NOTICE 'Dental Family data available for copying: %', family_data_dental;
                RAISE NOTICE 'Calling fn_apply_family_to_couple_coverage for dental...';
                RAISE NOTICE 'NOTE: This function only applies family values when couple volume=0 AND rate=0 AND premium=0';

                updated_couple_data_rtc_rtq_dental := sandf.fn_apply_family_to_couple_coverage(
                    'dental',
                    couple_data_dental,
                    family_data_dental
                );

                RAISE NOTICE 'Dental Couple data AFTER fn_apply_family_to_couple_coverage: %', updated_couple_data_rtc_rtq_dental;

                -- Update the couple data if it was changed
                IF updated_couple_data_rtc_rtq_dental != quote_json #> '{benefitPremiums,dentalCarePremium,couple}' THEN
                    quote_json := jsonb_set(quote_json, '{benefitPremiums,dentalCarePremium,couple}', updated_couple_data_rtc_rtq_dental);
                    RAISE NOTICE 'DENTAL COUPLE COVERAGE UPDATED: Family values were applied to couple because couple had volume=0 AND rate=0 AND premium=0';
                ELSE
                    RAISE NOTICE 'DENTAL COUPLE COVERAGE NOT CHANGED: Couple already had valid data (not all zeros), so family values were not applied';
                END IF;
            END;
        ELSE
            RAISE NOTICE 'Dental couple/family coverage check: couple exists=%, family exists=%',
                (quote_json #> '{benefitPremiums,dentalCarePremium}' ? 'couple'),
                (quote_json #> '{benefitPremiums,dentalCarePremium}' ? 'family');
        END IF;

        RAISE NOTICE '=== CALCULATING DENTAL VOLUMES AND PREMIUMS ===';
        RAISE NOTICE 'Dental Premium data structure: %', quote_json #> '{benefitPremiums,dentalCarePremium}';
        RAISE NOTICE 'Dental Volume calculation starting with: dental_vol = %, dental_prem = %', dental_vol, dental_prem;

        -- Calculate Dental volumes and premiums with safe conversion
        FOR x IN SELECT * FROM jsonb_each(quote_json #> '{benefitPremiums,dentalCarePremium}')
        LOOP
            RAISE NOTICE '--- Processing Dental coverage type: % ---', x.key;
            RAISE NOTICE 'Full coverage data: %', x.value;

            DECLARE
                volume_before NUMERIC := dental_vol;
                premium_before NUMERIC := dental_prem;
            BEGIN
                -- Volume processing
                BEGIN
                    safe_numeric_value := CASE
                        WHEN x.value ->> 'volume' IS NULL OR TRIM(x.value ->> 'volume') = '' THEN 0
                        ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'volume'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                    END;
                    RAISE NOTICE 'DENTAL % VOLUME PROCESSING:', x.key;
                    RAISE NOTICE '  Raw volume value: "%"', COALESCE(x.value ->> 'volume', 'NULL');
                    RAISE NOTICE '  Parsed volume value: %', safe_numeric_value;
                    RAISE NOTICE '  Volume before adding: %', volume_before;
                    dental_vol := dental_vol + safe_numeric_value;
                    RAISE NOTICE '  Volume after adding: % (added %)', dental_vol, safe_numeric_value;
                    RAISE NOTICE '  Volume calculation: % + % = %', volume_before, safe_numeric_value, dental_vol;
                EXCEPTION
                    WHEN OTHERS THEN
                        RAISE NOTICE 'ERROR parsing Dental % volume: %, using 0', x.key, SQLERRM;
                        dental_vol := dental_vol + 0;
                        RAISE NOTICE '  Volume after error (no change): %', dental_vol;
                END;

                -- Premium processing
                BEGIN
                    safe_numeric_value := CASE
                        WHEN x.value ->> 'premium' IS NULL OR TRIM(x.value ->> 'premium') = '' THEN 0
                        ELSE COALESCE((regexp_replace(TRIM(x.value ->> 'premium'), '[^0-9\.-]', '', 'g'))::NUMERIC, 0)
                    END;
                    RAISE NOTICE 'DENTAL % PREMIUM PROCESSING:', x.key;
                    RAISE NOTICE '  Raw premium value: "%"', COALESCE(x.value ->> 'premium', 'NULL');
                    RAISE NOTICE '  Parsed premium value: %', safe_numeric_value;
                    RAISE NOTICE '  Premium before adding: %', premium_before;
                    dental_prem := dental_prem + safe_numeric_value;
                    RAISE NOTICE '  Premium after adding: % (added %)', dental_prem, safe_numeric_value;
                    RAISE NOTICE '  Premium calculation: % + % = %', premium_before, safe_numeric_value, dental_prem;
                EXCEPTION
                    WHEN OTHERS THEN
                        RAISE NOTICE 'ERROR parsing Dental % premium: %, using 0', x.key, SQLERRM;
                        dental_prem := dental_prem + 0;
                        RAISE NOTICE '  Premium after error (no change): %', dental_prem;
                END;
            END;

            RAISE NOTICE '--- End Dental % processing ---', x.key;
        END LOOP;

        RAISE NOTICE '=== DENTAL VOLUME/PREMIUM CALCULATION COMPLETE ===';
        RAISE NOTICE 'DENTAL FINAL TOTALS - Volume: %, Premium: %', dental_vol, dental_prem;

        -- Calculate average premiums per unit volume (monthly * 12)
        ehc_avg := CASE WHEN ehc_vol > 0 THEN (ehc_prem / ehc_vol) * 12 ELSE 0 END;
        dental_avg := CASE WHEN dental_vol > 0 THEN (dental_prem / dental_vol) * 12 ELSE 0 END;

        RAISE NOTICE 'ehc_avg --: %', ehc_avg;
        RAISE NOTICE 'dental_avg --: %', dental_avg;

        -- Calculate targetClaimsValue with renewal charges
        -- Now using 0 instead of NULL checks since we default to 0
        IF (ehc_avg = 0 AND dental_avg = 0) THEN
            amount_text := '$0';
        ELSE
            BEGIN
                amount := (ehc_avg * (1 - (trend_ehc / 100)) * (1 - (ibnr_ehc / 100)) * (1 - (tlr_ehc / 100))) +
                         (dental_avg * (1 - (ibnr_dental / 100)) * (1 - (tlr_dental / 100)));
                RAISE NOTICE 'amount --: %', amount;
                amount_text := sandf.fn_format_currency_with_symbol_java_style(amount);
            EXCEPTION
                WHEN OTHERS THEN
                    amount_text := '$0';
                    amount := 0;
            END;
        END IF;

        RAISE NOTICE 'amount_text --: %', amount_text;

        -- Add to section lists
        section_list := section_list || jsonb_build_object('amount', amount_text);

        -- Calculate and format renewal charge percentage
        -- First check renewalModel - if 'Partially Pooled' or 'Pooled', return 'Pooled'
        DECLARE
            renewal_model_text TEXT;
        BEGIN
            renewal_model_text := quote_json #>> '{benefitPremiums,rateGuarantees,renewalModel}';

            IF renewal_model_text IS NOT NULL AND TRIM(renewal_model_text) != '' AND
               (TRIM(renewal_model_text) = 'Partially Pooled' OR TRIM(renewal_model_text) = 'Pooled') THEN
                renewal_percent_str := 'Pooled';
            ELSIF amount_text = '$0' OR (ehc_avg + dental_avg) = 0 THEN
                renewal_percent_str := '0%';
            ELSE
                BEGIN
                    renewal_percent := (1 - (amount / (ehc_avg + dental_avg))) * 100;
                    renewal_percent_str := COALESCE(TO_CHAR(CEIL(renewal_percent * 10) / 10, 'FM999990.0'), '0') || '%';
                EXCEPTION
                    WHEN OTHERS THEN
                        renewal_percent_str := '0%';
                END;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                -- If there's any error accessing renewalModel, fall back to normal calculation
                IF amount_text = '$0' OR (ehc_avg + dental_avg) = 0 THEN
                    renewal_percent_str := '0%';
                ELSE
                    BEGIN
                        renewal_percent := (1 - (amount / (ehc_avg + dental_avg))) * 100;
                        renewal_percent_str := COALESCE(TO_CHAR(CEIL(renewal_percent * 10) / 10, 'FM999990.0'), '0') || '%';
                    EXCEPTION
                        WHEN OTHERS THEN
                            renewal_percent_str := '0%';
                    END;
                END IF;
        END;

        renewal_section_list := renewal_section_list || jsonb_build_object(
            'renewalCharge', renewal_percent_str
        );
    END LOOP;

    -- Return final result
    RETURN jsonb_build_object(
        'targetedClaims', jsonb_build_object(
            'carriers', carrier_list,
            'sections', section_list
        ),
        'renewalCharges', jsonb_build_object(
            'carriers', carrier_list,
            'sections', renewal_section_list
        )
    );
EXCEPTION
    WHEN OTHERS THEN
        -- Global error handler - return empty result
        RAISE NOTICE 'Error in function: %', SQLERRM;
        RETURN jsonb_build_object(
            'targetedClaims', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            ),
            'renewalCharges', jsonb_build_object(
                'carriers', ARRAY[]::TEXT[],
                'sections', '[]'::JSONB
            )
        );
END;
$$;

        ]]>
        </sql>