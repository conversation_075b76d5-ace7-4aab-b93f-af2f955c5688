
NOTICE:  Current totals for Canada Life: EHC Prem=0, EHC Vol=0, Dental Prem=0, Dental Vol=0
NOTICE:  Setting rating factors for carrier: Canada Life using class: manager
NOTICE:  Rating factors set for Canada Life: trend_ehc=0, ibnr_ehc=0, tlr_ehc=0, ibnr_dental=0, tlr_dental=0, renewal_model=
NOTICE:  fn_apply_family_to_couple_coverage: Processing extendedHealth benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 0 (numeric: 0), rate: 0 (numeric: 0), premium: 125.91 (numeric: 125.91)
NOTICE:  fn_apply_family_to_couple_coverage: One or more couple values are empty/0, will apply family values
NOTICE:  fn_apply_family_to_couple_coverage: Applied family values to couple coverage for extendedHealth benefit
NOTICE:  fn_apply_family_to_couple_coverage: New couple data: {"rate": 0, "volume": 0, "premium": 166.32}
NOTICE:  Renewal Target Claims Multi-class: Applied family values to couple coverage for extendedHealth
NOTICE:  EHC single for manager: Volume: , Premium: 59.01, Carrier Total Volume: 0, Carrier Total Premium: 59.01
NOTICE:  EHC couple for manager: Volume: 0, Premium: 166.32, Carrier Total Volume: 0, Carrier Total Premium: 225.33
NOTICE:  EHC family for manager: Volume: 0, Premium: 166.32, Carrier Total Volume: 0, Carrier Total Premium: 391.65
NOTICE:  fn_apply_family_to_couple_coverage: Processing dental benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 0 (numeric: 0), rate: 0 (numeric: 0), premium: 120.15 (numeric: 120.15)
NOTICE:  fn_apply_family_to_couple_coverage: One or more couple values are empty/0, will apply family values
NOTICE:  fn_apply_family_to_couple_coverage: Applied family values to couple coverage for dental benefit
NOTICE:  fn_apply_family_to_couple_coverage: New couple data: {"rate": 0, "volume": 0, "premium": 172.03}
NOTICE:  Renewal Target Claims Multi-class: Applied family values to couple coverage for dental
NOTICE:  Dental single for manager: Volume: 0, Premium: 58.78, Carrier Total Volume: 0, Carrier Total Premium: 58.78
NOTICE:  Dental couple for manager: Volume: 0, Premium: 172.03, Carrier Total Volume: 0, Carrier Total Premium: 230.81
NOTICE:  Dental family for manager: Volume: 0, Premium: 172.03, Carrier Total Volume: 0, Carrier Total Premium: 402.84
NOTICE:  Updated carrier Canada Life totals after processing class manager: EHC Vol=0, EHC Prem=391.65, Dental Vol=0, Dental Prem=402.84
NOTICE:  --- End processing for carrier Canada Life class manager ---
NOTICE:  
NOTICE:  --- Processing carrier: Canada Life for class: production (Quote ID: 4367) ---
NOTICE:  Current totals for Canada Life: EHC Prem=391.65, EHC Vol=0, Dental Prem=402.84, Dental Vol=0
NOTICE:  Rating factors already set for carrier: Canada Life, skipping
NOTICE:  fn_apply_family_to_couple_coverage: Processing extendedHealth benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 0 (numeric: 0), rate: 0 (numeric: 0), premium: 125.91 (numeric: 125.91)
NOTICE:  fn_apply_family_to_couple_coverage: One or more couple values are empty/0, will apply family values
NOTICE:  fn_apply_family_to_couple_coverage: Applied family values to couple coverage for extendedHealth benefit
NOTICE:  fn_apply_family_to_couple_coverage: New couple data: {"rate": 0, "volume": 0, "premium": 166.32}
NOTICE:  Renewal Target Claims Multi-class: Applied family values to couple coverage for extendedHealth
NOTICE:  EHC single for production: Volume: , Premium: 59.01, Carrier Total Volume: 0, Carrier Total Premium: 450.66
NOTICE:  EHC couple for production: Volume: 0, Premium: 166.32, Carrier Total Volume: 0, Carrier Total Premium: 616.98
NOTICE:  EHC family for production: Volume: 0, Premium: 166.32, Carrier Total Volume: 0, Carrier Total Premium: 783.30
NOTICE:  fn_apply_family_to_couple_coverage: Processing dental benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 0 (numeric: 0), rate: 0 (numeric: 0), premium: 120.15 (numeric: 120.15)
NOTICE:  fn_apply_family_to_couple_coverage: One or more couple values are empty/0, will apply family values
NOTICE:  fn_apply_family_to_couple_coverage: Applied family values to couple coverage for dental benefit
NOTICE:  fn_apply_family_to_couple_coverage: New couple data: {"rate": 0, "volume": 0, "premium": 172.03}
NOTICE:  Renewal Target Claims Multi-class: Applied family values to couple coverage for dental
NOTICE:  Dental single for production: Volume: 0, Premium: 58.78, Carrier Total Volume: 0, Carrier Total Premium: 461.62
NOTICE:  Dental couple for production: Volume: 0, Premium: 172.03, Carrier Total Volume: 0, Carrier Total Premium: 633.65
NOTICE:  Dental family for production: Volume: 0, Premium: 172.03, Carrier Total Volume: 0, Carrier Total Premium: 805.68
NOTICE:  Updated carrier Canada Life totals after processing class production: EHC Vol=0, EHC Prem=783.30, Dental Vol=0, Dental Prem=805.68
NOTICE:  --- End processing for carrier Canada Life class production ---
NOTICE:  
NOTICE:  --- Processing carrier: Manulife for class: manager (Quote ID: 4402) ---
NOTICE:  Initializing carrier totals for: Manulife
NOTICE:  Current totals for Manulife: EHC Prem=0, EHC Vol=0, Dental Prem=0, Dental Vol=0
NOTICE:  Setting rating factors for carrier: Manulife using class: manager
NOTICE:  Rating factors set for Manulife: trend_ehc=0, ibnr_ehc=0, tlr_ehc=0, ibnr_dental=0, tlr_dental=0, renewal_model=
NOTICE:  fn_apply_family_to_couple_coverage: Processing extendedHealth benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 1 (numeric: 1), rate: 112.76 (numeric: 112.76), premium: 112.76 (numeric: 112.76)
NOTICE:  fn_apply_family_to_couple_coverage: All couple values are present, keeping original couple data
NOTICE:  fn_apply_family_to_couple_coverage: Kept original couple data for extendedHealth benefit
NOTICE:  EHC single for manager: Volume: 9, Premium: 501.57, Carrier Total Volume: 9, Carrier Total Premium: 501.57
NOTICE:  EHC couple for manager: Volume: 1, Premium: 112.76, Carrier Total Volume: 10, Carrier Total Premium: 614.33
NOTICE:  EHC family for manager: Volume: 0, Premium: 0, Carrier Total Volume: 10, Carrier Total Premium: 614.33
NOTICE:  fn_apply_family_to_couple_coverage: Processing dental benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 1 (numeric: 1), rate: 94.99 (numeric: 94.99), premium: 94.99 (numeric: 94.99)
NOTICE:  fn_apply_family_to_couple_coverage: All couple values are present, keeping original couple data
NOTICE:  fn_apply_family_to_couple_coverage: Kept original couple data for dental benefit
NOTICE:  Dental single for manager: Volume: 9, Premium: 386.37, Carrier Total Volume: 9, Carrier Total Premium: 386.37
NOTICE:  Dental couple for manager: Volume: 1, Premium: 94.99, Carrier Total Volume: 10, Carrier Total Premium: 481.36
NOTICE:  Dental family for manager: Volume: 0, Premium: 0, Carrier Total Volume: 10, Carrier Total Premium: 481.36
NOTICE:  Updated carrier Manulife totals after processing class manager: EHC Vol=10, EHC Prem=614.33, Dental Vol=10, Dental Prem=481.36
NOTICE:  --- End processing for carrier Manulife class manager ---
NOTICE:  
NOTICE:  --- Processing carrier: Manulife for class: production (Quote ID: 4402) ---
NOTICE:  Current totals for Manulife: EHC Prem=614.33, EHC Vol=10, Dental Prem=481.36, Dental Vol=10
NOTICE:  Rating factors already set for carrier: Manulife, skipping
NOTICE:  fn_apply_family_to_couple_coverage: Processing extendedHealth benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 1 (numeric: 1), rate: 112.76 (numeric: 112.76), premium: 112.76 (numeric: 112.76)
NOTICE:  fn_apply_family_to_couple_coverage: All couple values are present, keeping original couple data
NOTICE:  fn_apply_family_to_couple_coverage: Kept original couple data for extendedHealth benefit
NOTICE:  EHC single for production: Volume: 9, Premium: 501.57, Carrier Total Volume: 19, Carrier Total Premium: 1115.90
NOTICE:  EHC couple for production: Volume: 1, Premium: 112.76, Carrier Total Volume: 20, Carrier Total Premium: 1228.66
NOTICE:  EHC family for production: Volume: 0, Premium: 0, Carrier Total Volume: 20, Carrier Total Premium: 1228.66
NOTICE:  fn_apply_family_to_couple_coverage: Processing dental benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 1 (numeric: 1), rate: 94.99 (numeric: 94.99), premium: 94.99 (numeric: 94.99)
NOTICE:  fn_apply_family_to_couple_coverage: All couple values are present, keeping original couple data
NOTICE:  fn_apply_family_to_couple_coverage: Kept original couple data for dental benefit
NOTICE:  Dental single for production: Volume: 9, Premium: 386.37, Carrier Total Volume: 19, Carrier Total Premium: 867.73
NOTICE:  Dental couple for production: Volume: 1, Premium: 94.99, Carrier Total Volume: 20, Carrier Total Premium: 962.72
NOTICE:  Dental family for production: Volume: 0, Premium: 0, Carrier Total Volume: 20, Carrier Total Premium: 962.72
NOTICE:  Updated carrier Manulife totals after processing class production: EHC Vol=20, EHC Prem=1228.66, Dental Vol=20, Dental Prem=962.72
NOTICE:  --- End processing for carrier Manulife class production ---
NOTICE:  
NOTICE:  --- Processing carrier: Victor for class: manager (Quote ID: 4368) ---
NOTICE:  Initializing carrier totals for: Victor
NOTICE:  Current totals for Victor: EHC Prem=0, EHC Vol=0, Dental Prem=0, Dental Vol=0
NOTICE:  Setting rating factors for carrier: Victor using class: manager
NOTICE:  Rating factors set for Victor: trend_ehc=0, ibnr_ehc=0, tlr_ehc=0, ibnr_dental=0, tlr_dental=0, renewal_model=
NOTICE:  fn_apply_family_to_couple_coverage: Processing extendedHealth benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 0 (numeric: 0), rate: 0 (numeric: 0), premium: 0 (numeric: 0)
NOTICE:  fn_apply_family_to_couple_coverage: One or more couple values are empty/0, will apply family values
NOTICE:  fn_apply_family_to_couple_coverage: Applied family values to couple coverage for extendedHealth benefit
NOTICE:  fn_apply_family_to_couple_coverage: New couple data: {"rate": 107.38, "volume": 8, "premium": 859.04}
NOTICE:  Renewal Target Claims Multi-class: Applied family values to couple coverage for extendedHealth
NOTICE:  EHC single for manager: Volume: 4, Premium: 184.60, Carrier Total Volume: 4, Carrier Total Premium: 184.60
NOTICE:  EHC couple for manager: Volume: 8, Premium: 859.04, Carrier Total Volume: 12, Carrier Total Premium: 1043.64
NOTICE:  EHC family for manager: Volume: 8, Premium: 859.04, Carrier Total Volume: 20, Carrier Total Premium: 1902.68
NOTICE:  fn_apply_family_to_couple_coverage: Processing dental benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 0 (numeric: 0), rate: 0 (numeric: 0), premium: 0 (numeric: 0)
NOTICE:  fn_apply_family_to_couple_coverage: One or more couple values are empty/0, will apply family values
NOTICE:  fn_apply_family_to_couple_coverage: Applied family values to couple coverage for dental benefit
NOTICE:  fn_apply_family_to_couple_coverage: New couple data: {"rate": 86.84, "volume": 8, "premium": 694.72}
NOTICE:  Renewal Target Claims Multi-class: Applied family values to couple coverage for dental
NOTICE:  Dental single for manager: Volume: 4, Premium: 134.64, Carrier Total Volume: 4, Carrier Total Premium: 134.64
NOTICE:  Dental couple for manager: Volume: 8, Premium: 694.72, Carrier Total Volume: 12, Carrier Total Premium: 829.36
NOTICE:  Dental family for manager: Volume: 8, Premium: 694.72, Carrier Total Volume: 20, Carrier Total Premium: 1524.08
NOTICE:  Updated carrier Victor totals after processing class manager: EHC Vol=20, EHC Prem=1902.68, Dental Vol=20, Dental Prem=1524.08
NOTICE:  --- End processing for carrier Victor class manager ---
NOTICE:  
NOTICE:  --- Processing carrier: Victor for class: production (Quote ID: 4368) ---
NOTICE:  Current totals for Victor: EHC Prem=1902.68, EHC Vol=20, Dental Prem=1524.08, Dental Vol=20
NOTICE:  Rating factors already set for carrier: Victor, skipping
NOTICE:  fn_apply_family_to_couple_coverage: Processing extendedHealth benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 0 (numeric: 0), rate: 0 (numeric: 0), premium: 0 (numeric: 0)
NOTICE:  fn_apply_family_to_couple_coverage: One or more couple values are empty/0, will apply family values
NOTICE:  fn_apply_family_to_couple_coverage: Applied family values to couple coverage for extendedHealth benefit
NOTICE:  fn_apply_family_to_couple_coverage: New couple data: {"rate": 107.38, "volume": 8, "premium": 859.04}
NOTICE:  Renewal Target Claims Multi-class: Applied family values to couple coverage for extendedHealth
NOTICE:  EHC single for production: Volume: 4, Premium: 184.60, Carrier Total Volume: 24, Carrier Total Premium: 2087.28
NOTICE:  EHC couple for production: Volume: 8, Premium: 859.04, Carrier Total Volume: 32, Carrier Total Premium: 2946.32
NOTICE:  EHC family for production: Volume: 8, Premium: 859.04, Carrier Total Volume: 40, Carrier Total Premium: 3805.36
NOTICE:  fn_apply_family_to_couple_coverage: Processing dental benefit
NOTICE:  fn_apply_family_to_couple_coverage: Couple values - volume: 0 (numeric: 0), rate: 0 (numeric: 0), premium: 0 (numeric: 0)
NOTICE:  fn_apply_family_to_couple_coverage: One or more couple values are empty/0, will apply family values
NOTICE:  fn_apply_family_to_couple_coverage: Applied family values to couple coverage for dental benefit
NOTICE:  fn_apply_family_to_couple_coverage: New couple data: {"rate": 86.84, "volume": 8, "premium": 694.72}
NOTICE:  Renewal Target Claims Multi-class: Applied family values to couple coverage for dental
NOTICE:  Dental single for production: Volume: 4, Premium: 134.64, Carrier Total Volume: 24, Carrier Total Premium: 1658.72
NOTICE:  Dental couple for production: Volume: 8, Premium: 694.72, Carrier Total Volume: 32, Carrier Total Premium: 2353.44
NOTICE:  Dental family for production: Volume: 8, Premium: 694.72, Carrier Total Volume: 40, Carrier Total Premium: 3048.16
NOTICE:  Updated carrier Victor totals after processing class production: EHC Vol=40, EHC Prem=3805.36, Dental Vol=40, Dental Prem=3048.16
NOTICE:  --- End processing for carrier Victor class production ---
NOTICE:  
NOTICE:  === END STEP 4 ===
NOTICE:  
NOTICE:  === STEP 5: CALCULATING RENEWAL TARGETS ===
NOTICE:  Final carrier totals JSON: {"Victor": {"ehc_vol": 40, "tlr_ehc": 0, "ehc_prem": 3805.36, "ibnr_ehc": 0, "trend_ehc": 0, "dental_vol": 40, "tlr_dental": 0, "dental_prem": 3048.16, "ibnr_dental": 0, "renewal_model": "", "rating_factors_set": true}, "Manulife": {"ehc_vol": 20, "tlr_ehc": 0, "ehc_prem": 1228.66, "ibnr_ehc": 0, "trend_ehc": 0, "dental_vol": 20, "tlr_dental": 0, "dental_prem": 962.72, "ibnr_dental": 0, "renewal_model": "", "rating_factors_set": true}, "Canada Life": {"ehc_vol": 0, "tlr_ehc": 0, "ehc_prem": 783.30, "ibnr_ehc": 0, "trend_ehc": 0, "dental_vol": 0, "tlr_dental": 0, "dental_prem": 805.68, "ibnr_dental": 0, "renewal_model": "", "rating_factors_set": true}}
NOTICE:  === AGGREGATED TOTALS BY CARRIER (ACROSS ALL EMPLOYEE CLASSES) ===
NOTICE:  CARRIER: Victor
NOTICE:    EHC Total Premium (All Classes): $3,805.36
NOTICE:    EHC Total Volume (All Classes): 40
NOTICE:    Dental Total Premium (All Classes): $3,048.16
NOTICE:    Dental Total Volume (All Classes): 40
NOTICE:    Combined Premium Total (All Classes): $6,853.52
NOTICE:    Combined Volume Total (All Classes): 80
NOTICE:    Rating Factors:
NOTICE:      Trend EHC: 0
NOTICE:      IBNR EHC: 0
NOTICE:      TLR EHC: 0
NOTICE:      IBNR Dental: 0
NOTICE:      TLR Dental: 0
NOTICE:      Renewal Model: 
NOTICE:  ---
NOTICE:  CARRIER: Manulife
NOTICE:    EHC Total Premium (All Classes): $1,228.66
NOTICE:    EHC Total Volume (All Classes): 20
NOTICE:    Dental Total Premium (All Classes): $962.72
NOTICE:    Dental Total Volume (All Classes): 20
NOTICE:    Combined Premium Total (All Classes): $2,191.38
NOTICE:    Combined Volume Total (All Classes): 40
NOTICE:    Rating Factors:
NOTICE:      Trend EHC: 0
NOTICE:      IBNR EHC: 0
NOTICE:      TLR EHC: 0
NOTICE:      IBNR Dental: 0
NOTICE:      TLR Dental: 0
NOTICE:      Renewal Model: 
NOTICE:  ---
NOTICE:  CARRIER: Canada Life
NOTICE:    EHC Total Premium (All Classes): $783.30
NOTICE:    EHC Total Volume (All Classes): 0
NOTICE:    Dental Total Premium (All Classes): $805.68
NOTICE:    Dental Total Volume (All Classes): 0
NOTICE:    Combined Premium Total (All Classes): $1,588.98
NOTICE:    Combined Volume Total (All Classes): 0
NOTICE:    Rating Factors:
NOTICE:      Trend EHC: 0
NOTICE:      IBNR EHC: 0
NOTICE:      TLR EHC: 0
NOTICE:      IBNR Dental: 0
NOTICE:      TLR Dental: 0
NOTICE:      Renewal Model: 
NOTICE:  ---
NOTICE:  === END OF AGGREGATED CARRIER TOTALS ===
NOTICE:  
NOTICE:  === CALCULATING TARGETED CLAIMS AND RENEWAL CHARGES ===
NOTICE:  --- Processing calculations for carrier: Victor ---
NOTICE:  Input values for Victor: EHC Prem=3805.36, EHC Vol=40, Dental Prem=3048.16, Dental Vol=40
NOTICE:  Rating factors for Victor: trend_ehc=0, ibnr_ehc=0, tlr_ehc=0, ibnr_dental=0, tlr_dental=0
NOTICE:  Average calculations for Victor: ehc_avg=1141.6080000000000000 (monthly premium * 12), dental_avg=914.4480000000000000 (monthly premium * 12)
NOTICE:  Starting targeted claims calculation for Victor
NOTICE:  Calculation breakdown for Victor:
NOTICE:    EHC Component: 1141.6080000000000000 * (1 - 0/100) * (1 - 0/100) * (1 - 0/100)
NOTICE:    Dental Component: 914.4480000000000000 * (1 - 0/100) * (1 - 0/100)
NOTICE:  Carrier Victor calculated targeted claims amount: 2056.0560000000000000000000000000000000000000000000000000000000000000000000000000
NOTICE:  Formatted amount text: $2,056.06
NOTICE:  Starting renewal charge calculation for Victor
NOTICE:  Renewal model text: ""
NOTICE:  Calculating renewal percentage: (1 - (2056.0560000000000000000000000000000000000000000000000000000000000000000000000000 / 2056.0560000000000000)) * 100
NOTICE:  Raw renewal percentage: 0.0000000000000000000000000000000000000000000000000000000000000000000000000000
NOTICE:  Formatted renewal percentage: 0.0%
NOTICE:  Final results for carrier Victor: targeted_claims=$2,056.06, renewal_charge=0.0%
NOTICE:  --- End calculations for carrier Victor ---
NOTICE:  
NOTICE:  --- Processing calculations for carrier: Manulife ---
NOTICE:  Input values for Manulife: EHC Prem=1228.66, EHC Vol=20, Dental Prem=962.72, Dental Vol=20
NOTICE:  Rating factors for Manulife: trend_ehc=0, ibnr_ehc=0, tlr_ehc=0, ibnr_dental=0, tlr_dental=0
NOTICE:  Average calculations for Manulife: ehc_avg=737.1960000000000000 (monthly premium * 12), dental_avg=577.6320000000000000 (monthly premium * 12)
NOTICE:  Starting targeted claims calculation for Manulife
NOTICE:  Calculation breakdown for Manulife:
NOTICE:    EHC Component: 737.1960000000000000 * (1 - 0/100) * (1 - 0/100) * (1 - 0/100)
NOTICE:    Dental Component: 577.6320000000000000 * (1 - 0/100) * (1 - 0/100)
NOTICE:  Carrier Manulife calculated targeted claims amount: 1314.8280000000000000000000000000000000000000000000000000000000000000000000000000
NOTICE:  Formatted amount text: $1,314.83
NOTICE:  Starting renewal charge calculation for Manulife
NOTICE:  Renewal model text: ""
NOTICE:  Calculating renewal percentage: (1 - (1314.8280000000000000000000000000000000000000000000000000000000000000000000000000 / 1314.8280000000000000)) * 100
NOTICE:  Raw renewal percentage: 0.0000000000000000000000000000000000000000000000000000000000000000000000000000
NOTICE:  Formatted renewal percentage: 0.0%
NOTICE:  Final results for carrier Manulife: targeted_claims=$1,314.83, renewal_charge=0.0%
NOTICE:  --- End calculations for carrier Manulife ---
NOTICE:  
NOTICE:  --- Processing calculations for carrier: Canada Life ---
NOTICE:  Input values for Canada Life: EHC Prem=783.30, EHC Vol=0, Dental Prem=805.68, Dental Vol=0
NOTICE:  Rating factors for Canada Life: trend_ehc=0, ibnr_ehc=0, tlr_ehc=0, ibnr_dental=0, tlr_dental=0
NOTICE:  Average calculations for Canada Life: ehc_avg=0 (monthly premium * 12), dental_avg=0 (monthly premium * 12)
NOTICE:  Starting targeted claims calculation for Canada Life
NOTICE:  Both EHC and Dental averages are 0, setting amount to $0
NOTICE:  Starting renewal charge calculation for Canada Life
NOTICE:  Renewal model text: ""
NOTICE:  Amount is $0 or total average is 0, setting renewal charge to 0%
NOTICE:  Final results for carrier Canada Life: targeted_claims=$0, renewal_charge=0%
NOTICE:  --- End calculations for carrier Canada Life ---
NOTICE:  
NOTICE:  === END STEP 5 ===
NOTICE:  
NOTICE:  === FINAL RESULTS SUMMARY ===
NOTICE:  Final section_list (targeted claims): [{"amount": "$2,056.06"}, {"amount": "$1,314.83"}, {"amount": "$0"}]
NOTICE:  Final renewal_section_list (renewal charges): [{"renewalCharge": "0.0%"}, {"renewalCharge": "0.0%"}, {"renewalCharge": "0%"}]
NOTICE:  Final ordered_carriers_array: {Current,Manulife,Victor}
NOTICE:  === END FINAL RESULTS SUMMARY ===
NOTICE:  

Successfully run. Total query runtime: 105 msec.
1 rows affected.