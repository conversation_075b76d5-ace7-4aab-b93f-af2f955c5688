  <sql splitStatements="false">
            <![CDATA[
        CREATE OR REPLACE FUNCTION sandf.fn_get_rate_sheet_RTQ(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    v_plan_id INT;
    v_employee_class_id INT;
    quote_record RECORD;
    carrier_name TEXT;
    quote_json JSONB;
    benefit_premiums JSONB;
    carrier_list TEXT[] := ARRAY[]::TEXT[];
    sections_array JSONB := '[]'::jsonb;
    key_name TEXT;
    val JSONB;
    subkey_name TEXT;
    subval JSONB;
    benefit_values JSONB;
    section_obj JSONB;
    calculations_array JSONB := '[]'::jsonb;
    calc_obj JSONB;
    monthly_premiums_map JSONB := '{}'::jsonb;
    annual_premiums_map JSONB := '{}'::jsonb;
    first_annual_premium NUMERIC;
    carrier_order TEXT[] := ARRAY[]::TEXT[];
    carrier_order_map JSONB := '{}'::jsonb;
    carrier_quote_id_map JSONB := '{}'::jsonb;
    friendly_name TEXT;
    sub_friendly_name TEXT;
    section_display_name TEXT;
    temp_sections_array JSONB := '[]'::jsonb;
    ordered_section RECORD;
    coverage_order TEXT[] := ARRAY['single', 'couple', 'family'];
    coverage_type TEXT;
    quote_id BIGINT;
    quote_uuid UUID;
    carrier_order_list RECORD;
    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;
    -- New variables for premium calculation
    total_monthly_premium NUMERIC := 0;
    total_annual_premium NUMERIC := 0;
    premium_value NUMERIC;
    -- Config variables
    config_json JSONB;
    benefit_to_premium_map JSONB;
    mapped_key_name TEXT;
    should_include_key BOOLEAN;

    -- Helper variables to safely convert volume to number
    volume_numeric NUMERIC;
    volume_text TEXT;

BEGIN
    RAISE NOTICE 'Starting fn_get_rate_sheet_RTQ with parameters:';
    RAISE NOTICE 'plan_uuid_param: %', plan_uuid_param;
    RAISE NOTICE 'user_id_param: %', user_id_param;
    RAISE NOTICE 'includes_param: %', includes_param;
    RAISE NOTICE 'excludes_param: %', excludes_param;
    RAISE NOTICE 'includes_quotes_uuid: %', includes_quotes_uuid;

    -- Get configuration from config table
    SELECT json_data INTO config_json
    FROM config.json_storage
    WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
    LIMIT 1;

    RAISE NOTICE 'Config JSON found: %', CASE WHEN config_json IS NOT NULL THEN 'YES' ELSE 'NO' END;

    -- Extract benefitToPremium mapping
    IF config_json IS NOT NULL THEN
        benefit_to_premium_map := config_json -> 'benefitToPremium';
    ELSE
        benefit_to_premium_map := '{}'::jsonb;
    END IF;

    RAISE NOTICE 'Benefit to premium map: %', benefit_to_premium_map;

    -- Convert plan_uuid text to plan_id
    SELECT p.plan_id INTO v_plan_id
    FROM sandf.plan p
    WHERE p.plan_uuid = plan_uuid_param::uuid
    ORDER BY p.plan_id ASC
    LIMIT 1;

    RAISE NOTICE 'Found plan_id: %', v_plan_id;

    IF v_plan_id IS NULL THEN
        RAISE NOTICE 'No plan found for UUID: %, returning empty result', plan_uuid_param;
        RETURN jsonb_build_object('carriers', ARRAY[]::TEXT[], 'sections', '[]'::JSONB, 'calculations', '[]'::JSONB);
    END IF;

    -- Get first employee_class_id with rtq_set_flag TRUE
    SELECT employee_class_id INTO v_employee_class_id
    FROM sandf.employee_class
    WHERE plan_id = v_plan_id 
    ORDER BY employee_class_id ASC
    LIMIT 1;

    RAISE NOTICE 'Found employee_class_id with rtq_set_flag=TRUE: %', v_employee_class_id;

    IF v_employee_class_id IS NULL THEN
        RAISE NOTICE 'No employee class with rtq_set_flag=TRUE found for plan_id: %, returning empty result', v_plan_id;
        RETURN jsonb_build_object('carriers', ARRAY[]::TEXT[], 'sections', '[]'::JSONB, 'calculations', '[]'::JSONB);
    END IF;

    -- Build carrier order map using user preference
    RAISE NOTICE 'Building carrier order map for employee_class_id: %', v_employee_class_id;

    FOR quote_record IN
        SELECT ecq.*, q.quote_id, q.quote_uuid, c.description as carrier_name
        FROM sandf.employee_class_quote ecq
        JOIN sandf.quote q ON q.quote_id = ecq.quote_id
        JOIN sandf.carrier c ON q.carrier_id = c.carrier_id
        WHERE ecq.employee_class_id = v_employee_class_id
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
    LOOP
        RAISE NOTICE 'Found quote: quote_id=%, quote_uuid=%, carrier=%',
            quote_record.quote_id, quote_record.quote_uuid, quote_record.carrier_name;

        carrier_order_map := carrier_order_map || jsonb_build_object(
            quote_record.carrier_name,
            sandf.get_user_preference_order(
                user_id_param,
                plan_uuid_param,
                quote_record.quote_id,
                quote_record.quote_uuid,
                quote_record.employee_class_quote_id::INTEGER
            )
        );
        carrier_quote_id_map := carrier_quote_id_map || jsonb_build_object(
            quote_record.carrier_name,
            jsonb_build_object('quote_id', quote_record.quote_id, 'quote_uuid', quote_record.quote_uuid)
        );
    END LOOP;

    RAISE NOTICE 'Carrier order map: %', carrier_order_map;
    RAISE NOTICE 'Carrier quote ID map: %', carrier_quote_id_map;

    -- Build ordered carrier list and capture first carrier name
    RAISE NOTICE 'Building ordered carrier list...';

    FOR carrier_order_list IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (carrier_order_map ->> key)::int ASC, key ASC
    LOOP
        RAISE NOTICE 'Processing carrier: %, order: %',
            carrier_order_list.carrier_name, (carrier_order_map ->> carrier_order_list.carrier_name);

        -- Capture the first carrier name for display purposes
        IF first_carrier_name IS NULL THEN
            first_carrier_name := carrier_order_list.carrier_name;
            RAISE NOTICE 'Set first_carrier_name to: %', first_carrier_name;
        END IF;

        -- Use "Current" for the first carrier, otherwise use actual name
        IF carrier_order_list.carrier_name = first_carrier_name THEN
            display_carrier_name := 'Current';
        ELSE
            display_carrier_name := carrier_order_list.carrier_name;
        END IF;

        RAISE NOTICE 'Adding to carrier_list: %', display_carrier_name;
        carrier_list := array_append(carrier_list, display_carrier_name);
        carrier_order := array_append(carrier_order, carrier_order_list.carrier_name);
    END LOOP;

    RAISE NOTICE 'Final carrier_list: %', carrier_list;
    RAISE NOTICE 'Final carrier_order: %', carrier_order;

    -- Loop through quotes in user-preferred order
    RAISE NOTICE 'Starting main quote processing loop...';

    FOR quote_record IN
        SELECT ecq.*, q.quote_id, q.quote_uuid, c.description as carrier_name
        FROM sandf.employee_class_quote ecq
        JOIN sandf.quote q ON q.quote_id = ecq.quote_id
        JOIN sandf.carrier c ON q.carrier_id = c.carrier_id
        WHERE ecq.employee_class_id = v_employee_class_id
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
        ORDER BY sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            q.quote_id,
            q.quote_uuid,
            ecq.employee_class_quote_id::INTEGER
        ) ASC, c.description ASC
    LOOP
        RAISE NOTICE 'Processing quote for carrier: %', quote_record.carrier_name;

        quote_json := quote_record.formatted_quote_details::jsonb;
        RAISE NOTICE 'Quote JSON: %', quote_json;

        benefit_premiums := quote_json -> 'benefitPremiums';
        RAISE NOTICE 'Benefit premiums: %', benefit_premiums;

        carrier_name := quote_record.carrier_name;

        -- Initialize totals for this carrier
        total_monthly_premium := 0;
        total_annual_premium := 0;

        -- Loop over benefit premiums keys and values to calculate totals
        FOR key_name, val IN
            SELECT j.k1, j.v1
            FROM jsonb_each(benefit_premiums) AS j(k1, v1)
        LOOP
            -- Skip the pre-calculated total fields
            IF key_name NOT IN ('totalMonthlyPremiums', 'annualPremium') THEN
                -- Get mapped key name from config
				RAISE NOTICE 'benefit_to_premium_map --: %', benefit_to_premium_map;
                IF benefit_to_premium_map ? key_name THEN
                    mapped_key_name := benefit_to_premium_map ->> key_name;
                ELSE
                    mapped_key_name := key_name;
                END IF;
				RAISE NOTICE 'mapped_key_name --: %', mapped_key_name;

                -- Check if key should be included based on includes/excludes parameters
                should_include_key := FALSE;

                IF array_length(includes_param, 1) IS NULL THEN
                    -- No includes filter, check excludes only
                    IF array_length(excludes_param, 1) IS NULL OR NOT mapped_key_name = ANY(excludes_param) THEN
                        should_include_key := TRUE;
                    END IF;
                ELSE
                    -- Includes filter exists, key must be in includes and not in excludes
                    IF mapped_key_name = ANY(includes_param) THEN
                        IF array_length(excludes_param, 1) IS NULL OR NOT mapped_key_name = ANY(excludes_param) THEN
                            should_include_key := TRUE;
                        END IF;
                    END IF;
                END IF;

                RAISE NOTICE 'includes_param: %, excludes_param: %, key_name: %, mapped_key_name: %, should_include: %',
                    includes_param, excludes_param, key_name, mapped_key_name, should_include_key;

               IF should_include_key THEN
    -- Handle nested objects (single/couple/family)
    IF jsonb_typeof(val) = 'object' AND (val ? 'single' OR val ? 'couple' OR val ? 'family') THEN
        RAISE NOTICE 'Processing nested premium structure...';

        -- Apply special logic for extended health and dental couple coverage using global function
        IF (mapped_key_name = 'extendedHealth' OR mapped_key_name = 'dental') AND val ? 'couple' AND val ? 'family' THEN
            DECLARE
                updated_couple_data JSONB;
            BEGIN
                updated_couple_data := sandf.fn_apply_family_to_couple_coverage(
                    mapped_key_name,
                    val -> 'couple',
                    val -> 'family'
                );

                -- Update the couple data if it was changed
                IF updated_couple_data != val -> 'couple' THEN
                    val := jsonb_set(val, ARRAY['couple'], updated_couple_data);
                    RAISE NOTICE 'RTQ Premium Calculation: Applied family values to couple coverage for %', mapped_key_name;
                END IF;
            END;
        END IF;

        FOREACH coverage_type IN ARRAY coverage_order
        LOOP
            IF val ? coverage_type THEN
                subval := val -> coverage_type;

                -- Log current coverage type and subval
                RAISE NOTICE 'Coverage Type: %, Subval: %', coverage_type, subval::TEXT;

                IF subval ? 'premium' THEN
                    premium_value := sandf.safe_parse_numeric(subval ->> 'premium');

                    -- Log the parsed premium value
                    RAISE NOTICE 'Parsed Premium Value for %: %', coverage_type, premium_value;

                    total_monthly_premium := total_monthly_premium + premium_value;
                    total_annual_premium := total_annual_premium + (premium_value * 12);

                    -- Log running totals
                    RAISE NOTICE 'Running Total - Monthly: %, Annual: %', total_monthly_premium, total_annual_premium;
                END IF;
            END IF;
        END LOOP;

   ELSE
    -- Handle direct premium values
    RAISE NOTICE 'Processing direct premium value...';

    IF val ? 'premium' THEN
        -- Attempt to parse premium regardless of type (string or number)
        premium_value := sandf.safe_parse_numeric(val ->> 'premium');

        -- Log raw and parsed premium values
        RAISE NOTICE 'Raw Premium: %, Parsed Premium Value: %', val ->> 'premium', premium_value;

        total_monthly_premium := total_monthly_premium + premium_value;
        total_annual_premium := total_annual_premium + (premium_value * 12);

        -- Log running totals
        RAISE NOTICE 'Running Total - Monthly: %, Annual: %', total_monthly_premium, total_annual_premium;
    END IF;
END IF;

END IF;

            END IF;
        END LOOP;

        -- Store calculated totals for this carrier
        monthly_premiums_map := jsonb_set(
            monthly_premiums_map,
            ARRAY[carrier_name],
            to_jsonb(total_monthly_premium)
        );
        annual_premiums_map := jsonb_set(
            annual_premiums_map,
            ARRAY[carrier_name],
            to_jsonb(total_annual_premium)
        );

        -- Continue with sections processing
        FOR key_name, val IN
            SELECT j.k1, j.v1
            FROM jsonb_each(benefit_premiums) AS j(k1, v1)
        LOOP
            -- Skip the pre-calculated total fields
            IF key_name NOT IN ('totalMonthlyPremiums', 'annualPremium') THEN
                -- Get mapped key name from config
                IF benefit_to_premium_map ? key_name THEN
                    mapped_key_name := benefit_to_premium_map ->> key_name;
                ELSE
                    mapped_key_name := key_name;
                END IF;

                -- Check if key should be included based on includes/excludes parameters
                should_include_key := FALSE;

                IF array_length(includes_param, 1) IS NULL THEN
                    -- No includes filter, check excludes only
                    IF array_length(excludes_param, 1) IS NULL OR NOT mapped_key_name = ANY(excludes_param) THEN
                        should_include_key := TRUE;
                    END IF;
                ELSE
                    -- Includes filter exists, key must be in includes and not in excludes
                    IF mapped_key_name = ANY(includes_param) THEN
                        IF array_length(excludes_param, 1) IS NULL OR NOT mapped_key_name = ANY(excludes_param) THEN
                            should_include_key := TRUE;
                        END IF;
                    END IF;
                END IF;

                IF should_include_key THEN
                    SELECT friendly INTO friendly_name
                    FROM sandf.ui_field
                    WHERE name = key_name
                    LIMIT 1;
                    IF friendly_name IS NULL OR trim(friendly_name) = '' THEN
                        friendly_name := initcap(replace(key_name, '_', ' '));
                    END IF;
                    IF jsonb_typeof(val) = 'object' AND (val ? 'single' OR val ? 'couple' OR val ? 'family') THEN
                        -- Apply special logic for extended health and dental couple coverage using global function
                        IF (mapped_key_name = 'extendedHealth' OR mapped_key_name = 'dental') AND val ? 'couple' AND val ? 'family' THEN
                            DECLARE
                                updated_couple_data_sections JSONB;
                            BEGIN
                                updated_couple_data_sections := sandf.fn_apply_family_to_couple_coverage(
                                    mapped_key_name,
                                    val -> 'couple',
                                    val -> 'family'
                                );

                                -- Update the couple data if it was changed
                                IF updated_couple_data_sections != val -> 'couple' THEN
                                    val := jsonb_set(val, ARRAY['couple'], updated_couple_data_sections);
                                    RAISE NOTICE 'RTQ Sections: Applied family values to couple coverage for %', mapped_key_name;
                                END IF;
                            END;
                        END IF;

                        FOREACH coverage_type IN ARRAY coverage_order
                        LOOP
                            IF val ? coverage_type THEN
                                subkey_name := coverage_type;
                                subval := val -> coverage_type;
                                SELECT friendly INTO sub_friendly_name
                                FROM sandf.ui_field
                                WHERE name = subkey_name
                                LIMIT 1;
                                IF sub_friendly_name IS NULL OR trim(sub_friendly_name) = '' THEN
                                    sub_friendly_name := initcap(subkey_name);
                                END IF;
                                section_display_name := friendly_name || ' ' || sub_friendly_name;
                                section_obj := (
                                    SELECT elem FROM jsonb_array_elements(sections_array) elem
                                    WHERE elem->>'id' = key_name || '_' || subkey_name
                                    LIMIT 1
                                );
                                IF section_obj IS NOT NULL THEN
                                    benefit_values := section_obj->'values';
                                ELSE
                                    benefit_values := '{}'::jsonb;
                                END IF;

                                -- Convert volume to numeric, default to 0 if conversion fails
                                BEGIN
                                volume_text := COALESCE(subval->>'volume', '0');
                                 volume_numeric := sandf.safe_parse_numeric(volume_text);
                                EXCEPTION WHEN OTHERS THEN
                                volume_numeric := 0;
                                END;

                                -- Use "Current" for first carrier, otherwise use actual name
                                IF carrier_name = first_carrier_name THEN
                                    display_carrier_name := 'Current';
                                ELSE
                                    display_carrier_name := carrier_name;
                                END IF;

                                benefit_values := benefit_values || jsonb_build_object(
                                    display_carrier_name,
                                    jsonb_build_object(
                                        'rate', '$' || to_char(ROUND(COALESCE(sandf.safe_parse_numeric(subval->>'rate'), 0), 2), 'FM999,999,990.00'),
                                        'volume', to_jsonb(volume_numeric),
                                        'premium', '$' || to_char(ROUND(COALESCE(sandf.safe_parse_numeric(subval->>'premium'), 0), 2), 'FM999,999,990.00')
                                    )
                                );
                                section_obj := jsonb_build_object(
                                    'id', key_name || '_' || subkey_name,
                                    'name', section_display_name,
                                    'values', benefit_values,
                                    'sort_key', key_name || '_' || subkey_name,
                                    'coverage_order', array_position(coverage_order, coverage_type),
                                    'base_benefit', key_name,
                                    'coverage_type', subkey_name
                                );
                                sections_array := (
                                    SELECT COALESCE(jsonb_agg(elem), '[]'::jsonb)
                                    FROM jsonb_array_elements(sections_array) elem
                                    WHERE elem->>'id' != key_name || '_' || subkey_name
                                );
                                sections_array := sections_array || section_obj;
                            END IF;
                        END LOOP;
                    ELSE
                        section_obj := (
                            SELECT elem FROM jsonb_array_elements(sections_array) elem
                            WHERE elem->>'id' = key_name
                            LIMIT 1
                        );
                        IF section_obj IS NOT NULL THEN
                            benefit_values := section_obj->'values';
                        ELSE
                            benefit_values := '{}'::jsonb;
                        END IF;

                        -- Convert volume to numeric, default to 0 if conversion fails
                        BEGIN
                        volume_text := COALESCE(val->>'volume', '0');
                        volume_numeric := sandf.safe_parse_numeric(volume_text);
                        EXCEPTION WHEN OTHERS THEN
                        volume_numeric := 0;
                        END;

                        -- Use "Current" for first carrier, otherwise use actual name
                        IF carrier_name = first_carrier_name THEN
                            display_carrier_name := 'Current';
                        ELSE
                            display_carrier_name := carrier_name;
                        END IF;

                        benefit_values := benefit_values || jsonb_build_object(
                            display_carrier_name,
                            jsonb_build_object(
                                'rate', '$' || to_char(ROUND(COALESCE(sandf.safe_parse_numeric(val->>'rate'), 0), 2), 'FM999,999,990.00'),
                                'volume', to_jsonb(volume_numeric),
                                'premium', '$' || to_char(ROUND(COALESCE(sandf.safe_parse_numeric(val->>'premium'), 0), 2), 'FM999,999,990.00')
                            )
                        );
                        section_obj := jsonb_build_object(
                            'id', key_name,
                            'name', friendly_name,
                            'values', benefit_values,
                            'sort_key', key_name
                        );
                        sections_array := (
                            SELECT COALESCE(jsonb_agg(elem), '[]'::jsonb)
                            FROM jsonb_array_elements(sections_array) elem
                            WHERE elem->>'id' != key_name
                        );
                        sections_array := sections_array || section_obj;
                    END IF;
                END IF;
            END IF;
        END LOOP;
    END LOOP;

    -- Order sections by display_order from ui_field table, then by coverage order
    temp_sections_array := '[]'::jsonb;
    FOR ordered_section IN
        SELECT
            elem as section_data,
            COALESCE(uf.display_order, 999999) as sort_order,
            elem->>'sort_key' as section_key,
            COALESCE((elem->>'coverage_order')::INT, 999) as coverage_sort,
            elem->>'base_benefit' as base_benefit_name,
            elem->>'coverage_type' as coverage_type_name
        FROM jsonb_array_elements(sections_array) elem
        LEFT JOIN sandf.ui_field uf ON uf.name = COALESCE(elem->>'base_benefit',
            CASE
                WHEN elem->>'sort_key' LIKE '%_%' THEN
                    split_part(elem->>'sort_key', '_', 1)
                ELSE
                    elem->>'sort_key'
            END)
        ORDER BY
            sort_order ASC,
            base_benefit_name ASC,
            coverage_sort ASC,
            section_key ASC
    LOOP
        section_obj := ordered_section.section_data - 'sort_key' - 'coverage_order' - 'base_benefit' - 'coverage_type';
        temp_sections_array := temp_sections_array || section_obj;
    END LOOP;
    sections_array := temp_sections_array;

    -- Prepare calculations array with differences and percentages (formatted) in user-preferred order
    IF array_length(carrier_order, 1) > 0 THEN
        first_annual_premium := COALESCE(sandf.safe_parse_numeric(annual_premiums_map ->> carrier_order[1]), 0);
        FOREACH carrier_name IN ARRAY carrier_order LOOP
            -- Use "Current" for first carrier, otherwise use actual name
            IF carrier_name = first_carrier_name THEN
                display_carrier_name := 'Current';
            ELSE
                display_carrier_name := carrier_name;
            END IF;

            calc_obj := jsonb_build_object(
                'carrier', display_carrier_name,
                 'totalMonthlyPremiums', '$' || to_char(ROUND(COALESCE(sandf.safe_parse_numeric(monthly_premiums_map ->> carrier_name), 0), 2), 'FM999,999,990.00'),
                'annualPremium', '$' || to_char(ROUND(COALESCE(sandf.safe_parse_numeric(annual_premiums_map ->> carrier_name), 0), 2), 'FM999,999,990.00'),
                '$ Difference From #1',
                CASE
                    WHEN carrier_name = carrier_order[1] THEN '$0.00'::TEXT
                    WHEN COALESCE(sandf.safe_parse_numeric(annual_premiums_map ->> carrier_name), 0) = 0 THEN '$0.00'::TEXT
                    ELSE '$' || to_char(ROUND(first_annual_premium - COALESCE(sandf.safe_parse_numeric(annual_premiums_map ->> carrier_name), 0), 2), 'FM999,999,990.00')
                END,
                'Percentage Different From #1',
                CASE
                    WHEN carrier_name = carrier_order[1] THEN '0.00%'::TEXT
                    WHEN COALESCE(sandf.safe_parse_numeric(annual_premiums_map ->> carrier_name), 0) = 0 OR first_annual_premium = 0 THEN '0.00%'::TEXT
                    ELSE to_char(ROUND(((COALESCE(sandf.safe_parse_numeric(annual_premiums_map ->> carrier_name), 0) - first_annual_premium) / NULLIF(first_annual_premium, 0)) * 100, 2), 'FM999990.00') || '%'
                END
            );
            calculations_array := calculations_array || calc_obj;
        END LOOP;
    END IF;

    RAISE NOTICE 'Final result - carriers: %, sections count: %, calculations count: %',
        carrier_list, jsonb_array_length(sections_array), jsonb_array_length(calculations_array);

    RETURN jsonb_build_object(
        'carriers', carrier_list,
        'sections', sections_array,
        'calculations', calculations_array
    );
END;
$$;

        ]]>
        </sql>