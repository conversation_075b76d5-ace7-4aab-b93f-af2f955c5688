[{"carriers": ["Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "adad-manager", "name": "Accidental Death & Dismembermenet - manager", "benefits": [{"key": "terminationAgeADAD", "name": "Termination Age ADAD", "values": {"Victor": "Retirement or to age 70", "Manulife": "Retirement or to age 71", "Chambers Plan": "Retirement or to age 75"}, "section": "adad-manager"}]}, {"id": "adad-production", "name": "Accidental Death & Dismembermenet - production", "benefits": [{"key": "terminationAgeADAD", "name": "Termination Age ADAD", "values": {"Victor": "Retirement or to age 70", "Manulife": "Retirement or to age 71", "Chambers Plan": "Retirement or to age 75"}, "section": "adad-production"}]}, {"id": "dental-manager", "name": "Dental - manager", "benefits": [{"key": "annualDeductibleDental", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Manulife": "<PERSON>l", "Chambers Plan": "<PERSON>l"}, "section": "dental-manager"}, {"key": "basicCoInsurance", "name": "Basic Co-Insurance", "values": {"Victor": "80%", "Manulife": "80%", "Chambers Plan": "80%"}, "section": "dental-manager"}, {"key": "majorCoInsurance", "name": "Major Co-Insurance", "values": {"Victor": "50%", "Manulife": "50%", "Chambers Plan": "50%"}, "section": "dental-manager"}, {"key": "orthodonticCoInsurance", "name": "Orthodontic Co-Insurance", "values": {"Victor": "-", "Manulife": "-", "Chambers Plan": "-"}, "section": "dental-manager"}, {"key": "basicAndMajorDentalMaximum", "name": "Basic & Major Dental Maximum", "values": {"Victor": "$2,000", "Manulife": "$2,000", "Chambers Plan": "$2,000"}, "section": "dental-manager"}, {"key": "orthodonticLifetimeMaximum", "name": "Orthodontic Lifetime Maximum", "values": {"Victor": "-", "Manulife": "-", "Chambers Plan": "-"}, "section": "dental-manager"}, {"key": "dentalRecall", "name": "Recall", "values": {"Victor": "6 months", "Manulife": "6 months", "Chambers Plan": "2 times per year"}, "section": "dental-manager"}, {"key": "terminationAgeDental", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Manulife": "Retirement or to age 75", "Chambers Plan": "Retirement or to age 80"}, "section": "dental-manager"}]}]}, {"carriers": ["Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "dental-production", "name": "Dental - production", "benefits": [{"key": "annualDeductibleDental", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Manulife": "<PERSON>l", "Chambers Plan": "<PERSON>l"}, "section": "dental-production"}, {"key": "basicCoInsurance", "name": "Basic Co-Insurance", "values": {"Victor": "80%", "Manulife": "80%", "Chambers Plan": "80%"}, "section": "dental-production"}, {"key": "majorCoInsurance", "name": "Major Co-Insurance", "values": {"Victor": "50%", "Manulife": "50%", "Chambers Plan": "50%"}, "section": "dental-production"}, {"key": "orthodonticCoInsurance", "name": "Orthodontic Co-Insurance", "values": {"Victor": "-", "Manulife": "-", "Chambers Plan": "-"}, "section": "dental-production"}, {"key": "basicAndMajorDentalMaximum", "name": "Basic & Major Dental Maximum", "values": {"Victor": "$2,000", "Manulife": "$2,000", "Chambers Plan": "$2,000"}, "section": "dental-production"}, {"key": "orthodonticLifetimeMaximum", "name": "Orthodontic Lifetime Maximum", "values": {"Victor": "-", "Manulife": "-", "Chambers Plan": "-"}, "section": "dental-production"}, {"key": "dentalRecall", "name": "Recall", "values": {"Victor": "6 months", "Manulife": "6 months", "Chambers Plan": "2 times per year"}, "section": "dental-production"}, {"key": "terminationAgeDental", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Manulife": "Retirement or to age 75", "Chambers Plan": "Retirement or to age 80"}, "section": "dental-production"}]}, {"id": "dependentlife-manager", "name": "Dependent Life - manager", "benefits": [{"key": "spouse", "name": "Spouse", "values": {"Victor": "$10,000", "Manulife": "$5,000", "Chambers Plan": "$3,200"}, "section": "dependentlife-manager"}, {"key": "child", "name": "Child", "values": {"Victor": "$5,000", "Manulife": "$2,500", "Chambers Plan": "$3,200"}, "section": "dependentlife-manager"}]}]}, {"carriers": ["Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "dependentlife-production", "name": "Dependent Life - production", "benefits": [{"key": "spouse", "name": "Spouse", "values": {"Victor": "$10,000", "Manulife": "$5,000", "Chambers Plan": "$3,200"}, "section": "dependentlife-production"}, {"key": "child", "name": "Child", "values": {"Victor": "$5,000", "Manulife": "$2,500", "Chambers Plan": "$2,600"}, "section": "dependentlife-production"}]}, {"id": "lifeinsurance-manager", "name": "Life Insurance - manager", "benefits": [{"key": "coverageLife", "name": "Coverage LI", "values": {"Victor": "1 times annual earnings", "Manulife": "$25,000", "Chambers Plan": "1 times annual earnings"}, "section": "lifeinsurance-manager"}, {"key": "ageReduction", "name": "Age Reduction", "values": {"Victor": "50% at age 65", "Manulife": "50% at age 65", "Chambers Plan": "50% at age 65"}, "section": "lifeinsurance-manager"}, {"key": "terminationAgeLife", "name": "Termination Age LI", "values": {"Victor": "Retirement or to age 70", "Manulife": "Retirement or to age 75", "Chambers Plan": "Retirement or to age 75"}, "section": "lifeinsurance-manager"}]}, {"id": "lifeinsurance-production", "name": "Life Insurance - production", "benefits": [{"key": "coverageLife", "name": "Coverage LI", "values": {"Victor": "1 times annual earnings", "Manulife": "$25,000", "Chambers Plan": "1 times annual earnings"}, "section": "lifeinsurance-production"}, {"key": "ageReduction", "name": "Age Reduction", "values": {"Victor": "50% at age 65", "Manulife": "50% at age 65", "Chambers Plan": "50% at age 65"}, "section": "lifeinsurance-production"}, {"key": "terminationAgeLife", "name": "Termination Age LI", "values": {"Victor": "Retirement or to age 70", "Manulife": "Retirement or to age 75", "Chambers Plan": "Retirement or to age 75"}, "section": "lifeinsurance-production"}]}]}, {"carriers": ["Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "extendedhealth-manager", "name": "Extended Health - manager", "benefits": [{"key": "annualDeductibleEHC", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Manulife": "<PERSON>l", "Chambers Plan": "<PERSON>l"}, "section": "extendedhealth-manager"}, {"key": "paramedicalMaximum", "name": "Paramedical Maximum", "values": {"Victor": "$500", "Manulife": "$500", "Chambers Plan": "$500"}, "section": "extendedhealth-manager"}, {"key": "visionCare", "name": "Vision", "values": {"Victor": "$250/24 months", "Manulife": "$200/2 years", "Chambers Plan": "$200/24 months"}, "section": "extendedhealth-manager"}, {"key": "eyeExams", "name": "<PERSON>ams", "values": {"Victor": "Yes", "Manulife": "Yes", "Chambers Plan": "Yes"}, "section": "extendedhealth-manager"}, {"key": "privateDutyNursingMaximum", "name": "Private Duty Nursing Maximum", "values": {"Victor": "$10,000/year", "Manulife": "$10,000/year", "Chambers Plan": "$25,000/24 months"}, "section": "extendedhealth-manager"}, {"key": "semiPrivateHospital", "name": "Semi Private Hospital", "values": {"Victor": "Yes", "Manulife": "Yes", "Chambers Plan": "Yes"}, "section": "extendedhealth-manager"}, {"key": "hearingAids", "name": "Hearing Aids", "values": {"Victor": "$500/3 years", "Manulife": "$500/5 years", "Chambers Plan": "$700/60 months"}, "section": "extendedhealth-manager"}, {"key": "outOfCountryTravel", "name": "Out of Country Emergency", "values": {"Victor": "Yes", "Manulife": "Yes", "Chambers Plan": "Yes"}, "section": "extendedhealth-manager"}, {"key": "terminationAgeEHC", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Manulife": "Retirement or to age 75", "Chambers Plan": "Retirement or to age 80"}, "section": "extendedhealth-manager"}]}]}, {"carriers": ["Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "extendedhealth-production", "name": "Extended Health - production", "benefits": [{"key": "annualDeductibleEHC", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Manulife": "<PERSON>l", "Chambers Plan": "<PERSON>l"}, "section": "extendedhealth-production"}, {"key": "paramedicalMaximum", "name": "Paramedical Maximum", "values": {"Victor": "$500", "Manulife": "$500", "Chambers Plan": "$500"}, "section": "extendedhealth-production"}, {"key": "visionCare", "name": "Vision", "values": {"Victor": "$250/24 months", "Manulife": "$200/2 years", "Chambers Plan": "$200/24 months adults, 12 months children"}, "section": "extendedhealth-production"}, {"key": "eyeExams", "name": "<PERSON>ams", "values": {"Victor": "Yes", "Manulife": "Yes", "Chambers Plan": "Yes"}, "section": "extendedhealth-production"}, {"key": "privateDutyNursingMaximum", "name": "Private Duty Nursing Maximum", "values": {"Victor": "$10,000/year", "Manulife": "$10,000/year", "Chambers Plan": "$25,000/24 months"}, "section": "extendedhealth-production"}, {"key": "semiPrivateHospital", "name": "Semi Private Hospital", "values": {"Victor": "Yes", "Manulife": "Yes", "Chambers Plan": "Yes"}, "section": "extendedhealth-production"}, {"key": "hearingAids", "name": "Hearing Aids", "values": {"Victor": "$500/3 years", "Manulife": "$500/5 years", "Chambers Plan": "$700/60 months"}, "section": "extendedhealth-production"}, {"key": "outOfCountryTravel", "name": "Out of Country Emergency", "values": {"Victor": "Yes", "Manulife": "Yes", "Chambers Plan": "Yes"}, "section": "extendedhealth-production"}, {"key": "terminationAgeEHC", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Manulife": "Retirement or to age 75", "Chambers Plan": "Retirement or to age 80"}, "section": "extendedhealth-production"}]}]}, {"carriers": ["Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "<PERSON><PERSON>ness-manager", "name": "Critical Illness - manager", "benefits": [{"key": "amountCI", "name": "Amount", "values": {"Victor": "-", "Manulife": "$30,000", "Chambers Plan": "-"}, "section": "<PERSON><PERSON>ness-manager"}, {"key": "coverageCI", "name": "Coverage", "values": {"Victor": "-", "Manulife": "Employee Only", "Chambers Plan": "-"}, "section": "<PERSON><PERSON>ness-manager"}, {"key": "terminationAgeCI", "name": "Termination Age", "values": {"Victor": "-", "Manulife": "Retirement or to age 70", "Chambers Plan": "-"}, "section": "<PERSON><PERSON>ness-manager"}]}, {"id": "criticalillness-production", "name": "Critical Illness - production", "benefits": [{"key": "amountCI", "name": "Amount", "values": {"Victor": "-", "Manulife": "$30,000", "Chambers Plan": "-"}, "section": "criticalillness-production"}, {"key": "coverageCI", "name": "Coverage", "values": {"Victor": "-", "Manulife": "Employee Only", "Chambers Plan": "-"}, "section": "criticalillness-production"}, {"key": "terminationAgeCI", "name": "Termination Age", "values": {"Victor": "-", "Manulife": "Retirement or to age 70", "Chambers Plan": "-"}, "section": "criticalillness-production"}]}, {"id": "employeeassistance-manager", "name": "Employee Assistance - manager", "benefits": [{"key": "coverageEA", "name": "Coverage", "values": {"Victor": "Yes", "Manulife": "Yes", "Chambers Plan": "Yes"}, "section": "employeeassistance-manager"}]}, {"id": "employeeassistance-production", "name": "Employee Assistance - production", "benefits": [{"key": "coverageEA", "name": "Coverage", "values": {"Victor": "Yes", "Manulife": "Yes", "Chambers Plan": "Yes"}, "section": "employeeassistance-production"}]}]}, {"carriers": ["Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "longtermdisability-manager", "name": "Long Term Disability - manager", "benefits": [{"key": "coverageLTD", "name": "Coverage LTD", "values": {"Victor": "66.67%", "Manulife": "-", "Chambers Plan": "67% first $3,500, 50% balance"}, "section": "longtermdisability-manager"}, {"key": "eliminationPeriod", "name": "Elimination Period LTD", "values": {"Victor": "16 weeks", "Manulife": "182 days", "Chambers Plan": "113 days"}, "section": "longtermdisability-manager"}, {"key": "benefitPeriod", "name": "Benefit Period LTD", "values": {"Victor": "to age 65", "Manulife": "-", "Chambers Plan": "to age 65"}, "section": "longtermdisability-manager"}, {"key": "benefitMaximumLTD", "name": "Benefit Maximum LTD", "values": {"Victor": "$5,000", "Manulife": "-", "Chambers Plan": "$8,000"}, "section": "longtermdisability-manager"}, {"key": "definitionOfDisability", "name": "Definition of Disability", "values": {"Victor": "2 year own occupation", "Manulife": "any occupation", "Chambers Plan": "24 months own occupation"}, "section": "longtermdisability-manager"}, {"key": "quoteToNEMOrMax", "name": "Quote to NEM or Max", "values": {"Victor": "-", "Manulife": "-", "Chambers Plan": "-"}, "section": "longtermdisability-manager"}, {"key": "terminationAgeLTD", "name": "Termination Age LTD", "values": {"Victor": "Retirement or to age 65", "Manulife": "Retirement or to age 65", "Chambers Plan": "Retirement or to age 65"}, "section": "longtermdisability-manager"}, {"key": "nonEvidenceMaximum", "name": "Non Evidence Maximum", "values": {"Victor": "$4,500", "Manulife": "-", "Chambers Plan": "$4,000"}, "section": "longtermdisability-manager"}]}]}, {"carriers": ["Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "longtermdisability-production", "name": "Long Term Disability - production", "benefits": [{"key": "coverageLTD", "name": "Coverage LTD", "values": {"Victor": "66.67%", "Manulife": "-", "Chambers Plan": "67% first $3,500, 50% balance"}, "section": "longtermdisability-production"}, {"key": "eliminationPeriod", "name": "Elimination Period LTD", "values": {"Victor": "16 weeks", "Manulife": "182 days", "Chambers Plan": "113 days"}, "section": "longtermdisability-production"}, {"key": "benefitPeriod", "name": "Benefit Period LTD", "values": {"Victor": "to age 65", "Manulife": "-", "Chambers Plan": "to age 65"}, "section": "longtermdisability-production"}, {"key": "benefitMaximumLTD", "name": "Benefit Maximum LTD", "values": {"Victor": "$5,000", "Manulife": "-", "Chambers Plan": "$8,000"}, "section": "longtermdisability-production"}, {"key": "definitionOfDisability", "name": "Definition of Disability", "values": {"Victor": "2 year own occupation", "Manulife": "any occupation", "Chambers Plan": "24 months own occupation"}, "section": "longtermdisability-production"}, {"key": "quoteToNEMOrMax", "name": "Quote to NEM or Max", "values": {"Victor": "-", "Manulife": "-", "Chambers Plan": "-"}, "section": "longtermdisability-production"}, {"key": "terminationAgeLTD", "name": "Termination Age LTD", "values": {"Victor": "Retirement or to age 65", "Manulife": "Retirement or to age 65", "Chambers Plan": "Retirement or to age 65"}, "section": "longtermdisability-production"}, {"key": "nonEvidenceMaximum", "name": "Non Evidence Maximum", "values": {"Victor": "$4,500", "Manulife": "$25,000", "Chambers Plan": "$4,000"}, "section": "longtermdisability-production"}]}, {"id": "shorttermdisability-manager", "name": "shortTermDisabilitymanager", "benefits": []}, {"id": "shorttermdisability-production", "name": "shortTermDisabilityproduction", "benefits": []}]}]