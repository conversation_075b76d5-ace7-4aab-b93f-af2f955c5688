
      
      <sql splitStatements="false">
            <![CDATA[
            CREATE OR REPLACE FUNCTION sandf.fn_get_plan_design_report_multi_class(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$

DECLARE
    plan_details_key TEXT := 'planDetails';
    quote_record RECORD;
    plan_details JSONB;
    fields JSONB;
    field_item JSONB;
    carriers_array JSONB := '[]'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefits_array JSONB;
    benefit_obj JSONB;
    carrier_name TEXT;
    group_name TEXT;
    group_display_name TEXT;
    benefit_name TEXT;
    benefit_key TEXT;
    benefit_values JSONB := '{}'::jsonb;
    field_details JSONB;
    field_detail JSONB;
    carrier_value TEXT;
    section_map JSONB := '{}'::jsonb;
    section_original_names JSONB := '{}'::jsonb;
    section_names TEXT[];
    section_name TEXT;
    section_id TEXT;
    section_display_name TEXT;
    benefit_map JSONB := '{}'::jsonb;
    benefit_keys TEXT[];
    benefit_key_item TEXT;
    section_order_record RECORD;
    benefit_order_record RECORD;
    skip_fields TEXT[] := ARRAY['maximumLife', 'maximumADAD'];
    coverage_life_values JSONB := '{}'::jsonb;
    field_values_to_check JSONB := '{}'::jsonb;
    should_skip_field BOOLEAN;
    carrier_check TEXT;
    coverage_value TEXT;
    field_value TEXT;
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order INTEGER;
    quote_uuid_val UUID;
    MAX_BENEFITS_PER_PAGE INTEGER := 16;
    all_benefits JSONB := '[]'::jsonb;
    all_sections JSONB := '[]'::jsonb;
    current_page_benefits INTEGER := 0;
    current_page_sections_count INTEGER := 0;
    current_page_sections JSONB := '[]'::jsonb;
    current_section_benefits JSONB := '[]'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    total_benefits INTEGER;
    total_sections INTEGER;
    current_section_name TEXT;
    current_section_id TEXT;
    current_section_display_name TEXT;
    section_idx INTEGER;
    benefit_idx INTEGER;
    employee_classes TEXT[];
    employee_class_count INTEGER;
    current_employee_class TEXT;
    class_suffix TEXT;
    class_index INTEGER;
    class_suffix_extracted TEXT;
    page_object JSONB;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;

    -- Configuration variables for filtering and ordering
    config_plan_details JSONB;
    config_overview JSONB;
    config_section_key TEXT;
    config_section_info JSONB;
    config_display_items JSONB;
    mapped_section_key TEXT;
    carrier_has_data BOOLEAN;
    carriers_with_data TEXT[] := '{}';
    config_section_keys TEXT[];
    config_section_order INTEGER;
    config_json JSONB;
BEGIN
    -- Load configuration from config.json_storage table
    SELECT json_data INTO config_json
    FROM config.json_storage
    WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
    LIMIT 1;

    -- Extract planDetails from the configuration
    config_plan_details := config_json -> 'planDetails';

    -- Get the order of sections from config_plan_details keys (maintains order from config.json)
    SELECT array_agg(key ORDER BY ordinality) INTO config_section_keys
    FROM jsonb_each(config_plan_details) WITH ORDINALITY;

    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb
    AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid));
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid,
               ec.name as employee_class_name
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = ANY(employee_classes)
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
    LOOP
        carrier_name := quote_record.carrier_description;

        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999
        );

        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order,
                    'quote_id', quote_record.quote_id,
                    'quote_uuid', quote_record.quote_uuid
                )
            );
        END IF;
    END LOOP;

    -- Set the first carrier name for display purposes (before processing benefits)
    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
        LIMIT 1
    LOOP
        first_carrier_name := carrier_item;
    END LOOP;

    class_index := 1;
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        class_suffix := current_employee_class;
        FOR quote_record IN
            SELECT ecq.formatted_quote_details,
                   c.description as carrier_description,
                   q.quote_id,
                   q.quote_uuid
            FROM sandf.plan p
            JOIN sandf.quote q ON q.plan_id = p.plan_id
            JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
            JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
            JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
            WHERE p.plan_uuid = plan_uuid_param::uuid
            AND ec.name = current_employee_class
            AND ecq.formatted_quote_details IS NOT NULL
            AND ecq.formatted_quote_details != '{}'::jsonb
            AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
            ORDER BY
                COALESCE((carrier_order_map -> c.description ->> 'order')::integer, 999999) ASC,
                c.description ASC
        LOOP
            carrier_name := quote_record.carrier_description;
            plan_details := quote_record.formatted_quote_details -> plan_details_key;

            IF plan_details IS NOT NULL AND jsonb_typeof(plan_details) = 'object' THEN
                fields := plan_details -> 'fields';
                IF fields IS NOT NULL AND jsonb_typeof(fields) = 'array' THEN
                    FOR field_item IN SELECT jsonb_array_elements(fields)
                    LOOP
                        group_name := field_item ->> 'groupName';
                        IF group_name IS NULL THEN
                            group_name := field_item ->> 'name';
                        END IF;

                        -- Check if this section is in our configuration
                        mapped_section_key := NULL;
                        config_section_info := NULL;

                        -- Map lifeInsuranceADAD to both lifeInsurance and ADAD sections
                        IF group_name = 'lifeInsuranceADAD' THEN
                            -- Process as lifeInsurance first
                            IF config_plan_details ? 'lifeInsurance' THEN
                                mapped_section_key := 'lifeInsurance';
                                config_section_info := config_plan_details -> 'lifeInsurance';
                            END IF;
                        ELSIF config_plan_details ? group_name THEN
                            mapped_section_key := group_name;
                            config_section_info := config_plan_details -> group_name;
                        END IF;

                        -- Only process if section is in configuration
                        IF config_section_info IS NOT NULL THEN
                            group_display_name := config_section_info ->> mapped_section_key;
                            config_display_items := config_section_info -> 'displayItems';

                            IF (includes_param IS NULL OR group_name = ANY(includes_param))
                            AND (excludes_param IS NULL OR NOT (group_name = ANY(excludes_param))) THEN

                                -- For multi-class, append class suffix to section mapping
                                section_map := section_map || jsonb_build_object(
                                    mapped_section_key || class_suffix,
                                    group_display_name || ' - ' || current_employee_class
                                );

                                -- Store original section name for ui_field lookup
                                section_original_names := section_original_names || jsonb_build_object(
                                    mapped_section_key || class_suffix,
                                    mapped_section_key
                                );

                                field_details := field_item -> 'fields';
                                IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                                    FOR field_detail IN SELECT jsonb_array_elements(field_details)
                                    LOOP
                                        benefit_key := field_detail ->> 'name';
                                        carrier_value := field_detail ->> 'value';

                                        -- Only process fields that are in displayItems configuration
                                        IF config_display_items ? benefit_key THEN
                                            benefit_name := config_display_items ->> benefit_key;

                                            -- Skip processing if value is null, empty, or whitespace only
                                            IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                                CONTINUE; -- Skip this field entirely if no value
                                            ELSE
                                                -- Validate as numeric if appropriate
                                                BEGIN
                                                    PERFORM sandf.safe_parse_numeric(carrier_value);
                                                EXCEPTION WHEN OTHERS THEN
                                                    -- If validation fails, skip this field
                                                    CONTINUE;
                                                END;
                                            END IF;

                                            -- Use "Current" for first carrier, otherwise use actual name
                                            IF carrier_name = first_carrier_name THEN
                                                display_carrier_name := 'Current';
                                            ELSE
                                                display_carrier_name := carrier_name;
                                            END IF;

                                            IF benefit_key = 'coverageLife' THEN
                                                coverage_life_values := coverage_life_values || jsonb_build_object(
                                                    display_carrier_name || '_' || class_suffix,
                                                    carrier_value
                                                );
                                            END IF;

                                            IF benefit_key = ANY(skip_fields) THEN
                                                IF NOT field_values_to_check ? (benefit_key || '_' || class_suffix) THEN
                                                    field_values_to_check := field_values_to_check || jsonb_build_object(
                                                        benefit_key || '_' || class_suffix,
                                                        '{}'::jsonb
                                                    );
                                                END IF;
                                                field_values_to_check := jsonb_set(
                                                    field_values_to_check,
                                                    ARRAY[benefit_key || '_' || class_suffix, display_carrier_name],
                                                    to_jsonb(carrier_value)
                                                );
                                            END IF;

                                            benefit_key_item := mapped_section_key || class_suffix || '.' || benefit_key;

                                            IF NOT benefit_map ? benefit_key_item THEN
                                                benefit_map := benefit_map || jsonb_build_object(
                                                    benefit_key_item,
                                                    jsonb_build_object(
                                                        'name', benefit_name,
                                                        'key', benefit_key,
                                                        'section', lower(replace(replace(replace(mapped_section_key, ' ', ''), '&', ''), '''', '')) || '-' || lower(replace(replace(trim(class_suffix), ' ', ''), '''', '')),
                                                        'values', '{}'::jsonb
                                                    )
                                                );
                                            END IF;

                                            benefit_map := jsonb_set(
                                                benefit_map,
                                                ARRAY[benefit_key_item, 'values', display_carrier_name],
                                                to_jsonb(carrier_value)
                                            );
                                        END IF;
                                    END LOOP;
                                END IF;

                                -- Handle ADAD section separately for lifeInsuranceADAD
                                IF group_name = 'lifeInsuranceADAD' AND config_plan_details ? 'ADAD' THEN
                                    config_section_info := config_plan_details -> 'ADAD';
                                    group_display_name := config_section_info ->> 'ADAD';
                                    config_display_items := config_section_info -> 'displayItems';

                                    -- Add ADAD section mapping
                                    section_map := section_map || jsonb_build_object(
                                        'ADAD' || class_suffix,
                                        group_display_name || ' - ' || current_employee_class
                                    );

                                    section_original_names := section_original_names || jsonb_build_object(
                                        'ADAD' || class_suffix,
                                        'ADAD'
                                    );

                                    -- Process ADAD fields
                                    field_details := field_item -> 'fields';
                                    IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                                        FOR field_detail IN SELECT jsonb_array_elements(field_details)
                                        LOOP
                                            benefit_key := field_detail ->> 'name';
                                            carrier_value := field_detail ->> 'value';

                                            -- Only process ADAD fields that are in displayItems configuration
                                            IF config_display_items ? benefit_key THEN
                                                benefit_name := config_display_items ->> benefit_key;

                                                -- Skip processing if value is null, empty, or whitespace only
                                                IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                                    CONTINUE; -- Skip this field entirely if no value
                                                ELSE
                                                    BEGIN
                                                        PERFORM sandf.safe_parse_numeric(carrier_value);
                                                    EXCEPTION WHEN OTHERS THEN
                                                        -- If validation fails, skip this field
                                                        CONTINUE;
                                                    END;
                                                END IF;

                                                -- Use "Current" for first carrier, otherwise use actual name
                                                IF carrier_name = first_carrier_name THEN
                                                    display_carrier_name := 'Current';
                                                ELSE
                                                    display_carrier_name := carrier_name;
                                                END IF;

                                                IF benefit_key = ANY(skip_fields) THEN
                                                    IF NOT field_values_to_check ? (benefit_key || '_' || class_suffix) THEN
                                                        field_values_to_check := field_values_to_check || jsonb_build_object(
                                                            benefit_key || '_' || class_suffix,
                                                            '{}'::jsonb
                                                        );
                                                    END IF;
                                                    field_values_to_check := jsonb_set(
                                                        field_values_to_check,
                                                        ARRAY[benefit_key || '_' || class_suffix, display_carrier_name],
                                                        to_jsonb(carrier_value)
                                                    );
                                                END IF;

                                                benefit_key_item := 'ADAD' || class_suffix || '.' || benefit_key;

                                                IF NOT benefit_map ? benefit_key_item THEN
                                                    benefit_map := benefit_map || jsonb_build_object(
                                                        benefit_key_item,
                                                        jsonb_build_object(
                                                            'name', benefit_name,
                                                            'key', benefit_key,
                                                            'section', 'adad-' || lower(replace(replace(trim(class_suffix), ' ', ''), '''', '')),
                                                            'values', '{}'::jsonb
                                                        )
                                                    );
                                                END IF;

                                                benefit_map := jsonb_set(
                                                    benefit_map,
                                                    ARRAY[benefit_key_item, 'values', display_carrier_name],
                                                    to_jsonb(carrier_value)
                                                );
                                            END IF;
                                        END LOOP;
                                    END IF;
                                END IF;
                            END IF;
                        END IF;
                    END LOOP;
                ELSE
                    DECLARE
                        benefit_data JSONB;
                        field_key TEXT;
                        show_value TEXT;
                    BEGIN
                        FOR benefit_key IN SELECT jsonb_object_keys(plan_details)
                        LOOP
                            -- Check if this section is in our configuration
                            mapped_section_key := NULL;
                            config_section_info := NULL;

                            -- Map lifeInsuranceADAD to both lifeInsurance and ADAD sections
                            IF benefit_key = 'lifeInsuranceADAD' THEN
                                -- Process as lifeInsurance first
                                IF config_plan_details ? 'lifeInsurance' THEN
                                    mapped_section_key := 'lifeInsurance';
                                    config_section_info := config_plan_details -> 'lifeInsurance';
                                END IF;
                            ELSIF config_plan_details ? benefit_key THEN
                                mapped_section_key := benefit_key;
                                config_section_info := config_plan_details -> benefit_key;
                            END IF;

                            -- Only process if section is in configuration
                            IF config_section_info IS NOT NULL AND
                               (includes_param IS NULL OR benefit_key = ANY(includes_param)) AND
                               (excludes_param IS NULL OR NOT (benefit_key = ANY(excludes_param))) THEN

                                benefit_data := plan_details -> benefit_key;

                                IF benefit_data IS NULL OR jsonb_typeof(benefit_data) != 'object' THEN
                                    CONTINUE;
                                END IF;

                                show_value := benefit_data ->> 'show';
                                IF show_value IS NOT NULL AND show_value::boolean = false THEN
                                    CONTINUE;
                                END IF;

                                section_display_name := config_section_info ->> mapped_section_key;
                                config_display_items := config_section_info -> 'displayItems';

                                section_map := section_map || jsonb_build_object(
                                    mapped_section_key || class_suffix,
                                    section_display_name || ' - ' || current_employee_class
                                );

                                section_original_names := section_original_names || jsonb_build_object(
                                    mapped_section_key || class_suffix,
                                    mapped_section_key
                                );

                                FOR field_key IN SELECT jsonb_object_keys(benefit_data)
                                LOOP
                                    IF field_key = 'show' THEN
                                        CONTINUE;
                                    END IF;

                                    -- Only process fields that are in displayItems configuration
                                    IF config_display_items ? field_key THEN
                                        benefit_name := config_display_items ->> field_key;

                                        carrier_value := benefit_data ->> field_key;
                                        -- Skip processing if value is null, empty, or whitespace only
                                        IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                            CONTINUE; -- Skip this field entirely if no value
                                        ELSE
                                            -- Validate as numeric if appropriate
                                            BEGIN
                                                PERFORM sandf.safe_parse_numeric(carrier_value);
                                            EXCEPTION WHEN OTHERS THEN
                                                -- If validation fails, skip this field
                                                CONTINUE;
                                            END;
                                        END IF;

                                        -- Use "Current" for first carrier, otherwise use actual name
                                        IF carrier_name = first_carrier_name THEN
                                            display_carrier_name := 'Current';
                                        ELSE
                                            display_carrier_name := carrier_name;
                                        END IF;

                                        IF field_key = 'coverageLife' THEN
                                            coverage_life_values := coverage_life_values || jsonb_build_object(
                                                display_carrier_name || '_' || class_suffix,
                                                carrier_value
                                            );
                                        END IF;

                                        IF field_key = ANY(skip_fields) THEN
                                            IF NOT field_values_to_check ? (field_key || '_' || class_suffix) THEN
                                                field_values_to_check := field_values_to_check || jsonb_build_object(
                                                    field_key || '_' || class_suffix,
                                                    '{}'::jsonb
                                                );
                                            END IF;
                                            field_values_to_check := jsonb_set(
                                                field_values_to_check,
                                                ARRAY[field_key || '_' || class_suffix, display_carrier_name],
                                                to_jsonb(carrier_value)
                                            );
                                        END IF;

                                        benefit_key_item := mapped_section_key || class_suffix || '.' || field_key;

                                        IF NOT benefit_map ? benefit_key_item THEN
                                            benefit_map := benefit_map || jsonb_build_object(
                                                benefit_key_item,
                                                jsonb_build_object(
                                                    'name', benefit_name,
                                                    'key', field_key,
                                                    'section', lower(replace(replace(replace(mapped_section_key, ' ', ''), '&', ''), '''', '')) || '-' || lower(replace(replace(trim(class_suffix), ' ', ''), '''', '')),
                                                    'values', '{}'::jsonb
                                                )
                                            );
                                        END IF;

                                        benefit_map := jsonb_set(
                                            benefit_map,
                                            ARRAY[benefit_key_item, 'values', display_carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;
                                END LOOP;

                                -- Handle ADAD section separately for lifeInsuranceADAD
                                IF benefit_key = 'lifeInsuranceADAD' AND config_plan_details ? 'ADAD' THEN
                                    config_section_info := config_plan_details -> 'ADAD';
                                    section_display_name := config_section_info ->> 'ADAD';
                                    config_display_items := config_section_info -> 'displayItems';

                                    -- Add ADAD section mapping
                                    section_map := section_map || jsonb_build_object(
                                        'ADAD' || class_suffix,
                                        section_display_name || ' - ' || current_employee_class
                                    );

                                    section_original_names := section_original_names || jsonb_build_object(
                                        'ADAD' || class_suffix,
                                        'ADAD'
                                    );

                                    -- Process ADAD fields
                                    FOR field_key IN SELECT jsonb_object_keys(benefit_data)
                                    LOOP
                                        IF field_key = 'show' THEN
                                            CONTINUE;
                                        END IF;

                                        -- Only process ADAD fields that are in displayItems configuration
                                        IF config_display_items ? field_key THEN
                                            benefit_name := config_display_items ->> field_key;

                                            carrier_value := benefit_data ->> field_key;
                                            -- Skip processing if value is null, empty, or whitespace only
                                            IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                                CONTINUE; -- Skip this field entirely if no value
                                            ELSE
                                                BEGIN
                                                    PERFORM sandf.safe_parse_numeric(carrier_value);
                                                EXCEPTION WHEN OTHERS THEN
                                                    -- If validation fails, skip this field
                                                    CONTINUE;
                                                END;
                                            END IF;

                                            -- Use "Current" for first carrier, otherwise use actual name
                                            IF carrier_name = first_carrier_name THEN
                                                display_carrier_name := 'Current';
                                            ELSE
                                                display_carrier_name := carrier_name;
                                            END IF;

                                            IF field_key = ANY(skip_fields) THEN
                                                IF NOT field_values_to_check ? (field_key || '_' || class_suffix) THEN
                                                    field_values_to_check := field_values_to_check || jsonb_build_object(
                                                        field_key || '_' || class_suffix,
                                                        '{}'::jsonb
                                                    );
                                                END IF;
                                                field_values_to_check := jsonb_set(
                                                    field_values_to_check,
                                                    ARRAY[field_key || '_' || class_suffix, display_carrier_name],
                                                    to_jsonb(carrier_value)
                                                );
                                            END IF;

                                            benefit_key_item := 'ADAD' || class_suffix || '.' || field_key;

                                            IF NOT benefit_map ? benefit_key_item THEN
                                                benefit_map := benefit_map || jsonb_build_object(
                                                    benefit_key_item,
                                                    jsonb_build_object(
                                                        'name', benefit_name,
                                                        'key', field_key,
                                                        'section', 'adad-' || lower(replace(replace(trim(class_suffix), ' ', ''), '''', '')),
                                                        'values', '{}'::jsonb
                                                    )
                                                );
                                            END IF;

                                            benefit_map := jsonb_set(
                                                benefit_map,
                                                ARRAY[benefit_key_item, 'values', display_carrier_name],
                                                to_jsonb(carrier_value)
                                            );
                                        END IF;
                                    END LOOP;
                                END IF;
                            END IF;
                        END LOOP;
                    END;
                END IF;
            END IF;
        END LOOP;

        class_index := class_index + 1;
    END LOOP;

    -- Normalize benefit_map to ensure all benefits have values for all carriers
    DECLARE
        all_carriers TEXT[];
        benefit_key_norm TEXT;
        carrier_name_norm TEXT;
        benefit_values_norm JSONB;
        carrier_has_any_data BOOLEAN;
        benefit_value TEXT;
    BEGIN
        -- Include all carriers that were in the original carrier_order_map
        -- This ensures carriers specified in includes_quotes_uuid are not filtered out
        SELECT array_agg(DISTINCT key ORDER BY key) INTO all_carriers
        FROM jsonb_each(carrier_order_map);

        -- Build ordered carriers array with display names (like RTQ version)
        ordered_carriers_array := '[]'::jsonb;
        FOR carrier_item IN
            SELECT key as carrier_name
            FROM jsonb_each(carrier_order_map)
            ORDER BY (value ->> 'order')::integer ASC, key ASC
        LOOP
            -- Use "Current" for the first carrier, otherwise use actual name
            IF carrier_item = first_carrier_name THEN
                display_carrier_name := 'Current';
            ELSE
                display_carrier_name := carrier_item;
            END IF;

            ordered_carriers_array := ordered_carriers_array || jsonb_build_array(display_carrier_name);
        END LOOP;

        -- Extract display names from ordered_carriers_array for normalization
        SELECT array_agg(value::text) INTO carriers_with_data
        FROM jsonb_array_elements_text(ordered_carriers_array);

        -- For each benefit, ensure all carriers with data are present
        FOR benefit_key_norm IN SELECT jsonb_object_keys(benefit_map)
        LOOP
            benefit_values_norm := benefit_map -> benefit_key_norm -> 'values';

            -- Add missing carriers with "-" value using display names
            FOREACH carrier_name_norm IN ARRAY carriers_with_data
            LOOP
                IF NOT (benefit_values_norm ? carrier_name_norm) THEN
                    benefit_map := jsonb_set(
                        benefit_map,
                        ARRAY[benefit_key_norm, 'values', carrier_name_norm],
                        to_jsonb('-'::text)
                    );
                END IF;
            END LOOP;

            -- Remove carriers that don't have data from this benefit
            FOR carrier_name_norm IN SELECT jsonb_object_keys(benefit_values_norm)
            LOOP
                IF NOT (carrier_name_norm = ANY(carriers_with_data)) THEN
                    benefit_map := jsonb_set(
                        benefit_map,
                        ARRAY[benefit_key_norm, 'values'],
                        (benefit_map -> benefit_key_norm -> 'values') - carrier_name_norm
                    );
                END IF;
            END LOOP;
        END LOOP;

        -- Keep all carriers in carrier_order_map (don't remove any)
        -- This ensures all carriers specified in includes_quotes_uuid are preserved
    END;

    -- ordered_carriers_array is already built during normalization above
    FOR section_order_record IN
        SELECT
            sm.key as section_name,
            COALESCE(
                (config_plan_details -> son.value ->> 'order')::integer,
                999999
            ) as sort_order,
            son.value as original_section_name
        FROM jsonb_each_text(section_map) sm
        LEFT JOIN jsonb_each_text(section_original_names) son ON sm.key = son.key
        ORDER BY sort_order ASC, son.value ASC, sm.key ASC
    LOOP
        section_name := section_order_record.section_name;
        class_suffix_extracted := replace(section_name, section_order_record.original_section_name, '');
        section_id := lower(replace(replace(replace(section_order_record.original_section_name, ' ', ''), '&', ''), '''', '')) || '-' || lower(replace(replace(trim(class_suffix_extracted), ' ', ''), '''', ''));
        section_display_name := COALESCE(section_map ->> section_name, section_name);
        benefits_array := '[]'::jsonb;
        FOR benefit_order_record IN
            SELECT
                key as benefit_key_item,
                value as benefit_obj,
                COALESCE(uf.display_order, 999999) as sort_order
            FROM jsonb_each(benefit_map)
            LEFT JOIN sandf.ui_field uf ON uf.name = (value ->> 'key')
            WHERE (value ->> 'section') = section_id
            ORDER BY sort_order ASC, (value ->> 'key') ASC
        LOOP
            benefit_obj := benefit_order_record.benefit_obj;
            should_skip_field := FALSE;

            IF (benefit_obj ->> 'key') = ANY(skip_fields) THEN
                should_skip_field := TRUE;
                class_suffix := regexp_replace(section_name, '^.*([A-Za-z]+)$', '\1');

                FOR carrier_check IN SELECT unnest(carriers_with_data)
                LOOP
                    coverage_value := coverage_life_values ->> (carrier_check || '_' || class_suffix);
                    field_value := (field_values_to_check -> ((benefit_obj ->> 'key') || '_' || class_suffix)) ->> carrier_check;

                    IF coverage_value != field_value THEN
                        should_skip_field := FALSE;
                        EXIT;
                    END IF;
                END LOOP;
            END IF;

            -- Check if benefit has any non-empty values across all carriers
            IF NOT should_skip_field THEN
                carrier_has_data := FALSE;
                FOR carrier_item IN SELECT jsonb_object_keys(benefit_obj -> 'values')
                LOOP
                    carrier_value := benefit_obj -> 'values' ->> carrier_item;
                    IF carrier_value IS NOT NULL AND trim(carrier_value) != '' AND carrier_value != '-' THEN
                        carrier_has_data := TRUE;
                        EXIT;
                    END IF;
                END LOOP;

                -- Only add benefit if it has data and shouldn't be skipped
                IF carrier_has_data THEN
                    benefits_array := benefits_array || jsonb_build_array(benefit_obj);
                END IF;
            END IF;
        END LOOP;

        -- Only create section object if it has benefits (skip empty sections)
        IF jsonb_array_length(benefits_array) > 0 THEN
            section_obj := jsonb_build_object(
                'name', section_display_name,
                'id', section_id,
                'benefits', benefits_array
            );

            -- Fix: Add section object directly to array, not wrapped in another array
            all_sections := all_sections || section_obj;
        END IF;
    END LOOP;

    -- Calculate total benefits and sections for pagination decision
    total_benefits := 0;
    total_sections := jsonb_array_length(all_sections);
    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        total_benefits := total_benefits + jsonb_array_length(all_sections -> section_idx -> 'benefits');
    END LOOP;

    -- If all sections fit in one page (considering both benefits and sections), return single page
    -- Each section adds visual weight, so we count sections + benefits for better pagination
    IF (total_benefits + total_sections) <= MAX_BENEFITS_PER_PAGE THEN
        RETURN jsonb_build_array(
            jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', all_sections
            )
        );
    END IF;

    -- Pagination logic - never break sections, only add complete sections to pages
    current_page_benefits := 0;
    current_page_sections_count := 0;
    current_page_sections := '[]'::jsonb;
    result_pages := '[]'::jsonb;

    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        section_obj := all_sections -> section_idx;
        benefits_array := section_obj -> 'benefits';

        -- Get the number of benefits in this section
        DECLARE
            section_benefit_count INTEGER := jsonb_array_length(benefits_array);
        BEGIN
            -- Check if adding this complete section would exceed the page limit
            -- Count both benefits and sections (each section adds visual weight)
            IF current_page_benefits > 0 AND (current_page_benefits + current_page_sections_count + section_benefit_count + 1) > MAX_BENEFITS_PER_PAGE THEN
                -- Current page is full, create a new page
                page_object := jsonb_build_object(
                    'carriers', ordered_carriers_array,
                    'sections', current_page_sections
                );
                result_pages := result_pages || jsonb_build_array(page_object);

                -- Reset for new page
                current_page_sections := '[]'::jsonb;
                current_page_benefits := 0;
                current_page_sections_count := 0;
            END IF;

            -- Add the complete section to current page
            -- Fix: Add section object directly to array, not wrapped in another array
            current_page_sections := current_page_sections || section_obj;
            current_page_benefits := current_page_benefits + section_benefit_count;
            current_page_sections_count := current_page_sections_count + 1;
        END;
    END LOOP;

    -- Add the final page if it has any sections
    IF jsonb_array_length(current_page_sections) > 0 THEN
        page_object := jsonb_build_object(
            'carriers', ordered_carriers_array,
            'sections', current_page_sections
        );
        result_pages := result_pages || jsonb_build_array(page_object);
    END IF;

    RETURN result_pages;

END;
$$;
]]>
        </sql>