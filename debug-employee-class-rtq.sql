-- Debug script to check employee class RTQ settings
-- Run this to diagnose the RTQ flag issue

-- 1. Check the plan details
SELECT 
    p.plan_id,
    p.plan_uuid,
    p.plan_name,
    p.created_date
FROM sandf.plan p 
WHERE p.plan_uuid = '6bfca487-5e29-4eab-85c4-8626a5810142';

-- 2. Check all employee classes for this plan
SELECT
    ec.employee_class_id,
    ec.plan_id,
    ec.name as class_name,
    ec.rtq_set_flag,
    ec.created_date,
    ec.updated_date
FROM sandf.employee_class ec
JOIN sandf.plan p ON p.plan_id = ec.plan_id
WHERE p.plan_uuid = '6bfca487-5e29-4eab-85c4-8626a5810142'
ORDER BY ec.employee_class_id;

-- 3. Check if there are any quotes for these employee classes
SELECT
    ec.employee_class_id,
    ec.name as class_name,
    ec.rtq_set_flag,
    COUNT(ecq.employee_class_quote_id) as quote_count,
    STRING_AGG(DISTINCT c.description, ', ') as carriers
FROM sandf.employee_class ec
JOIN sandf.plan p ON p.plan_id = ec.plan_id
LEFT JOIN sandf.employee_class_quote ecq ON ecq.employee_class_id = ec.employee_class_id
LEFT JOIN sandf.quote q ON q.quote_id = ecq.quote_id
LEFT JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
WHERE p.plan_uuid = '6bfca487-5e29-4eab-85c4-8626a5810142'
GROUP BY ec.employee_class_id, ec.name, ec.rtq_set_flag
ORDER BY ec.employee_class_id;

-- 4. Check the specific quote UUID you're filtering by
SELECT
    q.quote_id,
    q.quote_uuid,
    c.description as carrier_name,
    ecq.employee_class_id,
    ec.name as class_name,
    ec.rtq_set_flag
FROM sandf.quote q
JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
JOIN sandf.employee_class_quote ecq ON ecq.quote_id = q.quote_id
JOIN sandf.employee_class ec ON ec.employee_class_id = ecq.employee_class_id
JOIN sandf.plan p ON p.plan_id = ec.plan_id
WHERE q.quote_uuid = '76cc77de-c6ce-4cdc-bead-27706038d059'
AND p.plan_uuid = '6bfca487-5e29-4eab-85c4-8626a5810142';

-- 5. Suggested fix: Update the first employee class to have rtq_set_flag = TRUE
-- UNCOMMENT THE LINES BELOW AFTER REVIEWING THE RESULTS ABOVE

/*
UPDATE sandf.employee_class 
SET rtq_set_flag = TRUE,
    updated_date = CURRENT_TIMESTAMP
WHERE employee_class_id = (
    SELECT ec.employee_class_id
    FROM sandf.employee_class ec
    JOIN sandf.plan p ON p.plan_id = ec.plan_id
    WHERE p.plan_uuid = '6bfca487-5e29-4eab-85c4-8626a5810142'
    ORDER BY ec.employee_class_id ASC
    LIMIT 1
);
*/

-- 6. Verify the update worked
/*
SELECT
    ec.employee_class_id,
    ec.name as class_name,
    ec.rtq_set_flag,
    ec.updated_date
FROM sandf.employee_class ec
JOIN sandf.plan p ON p.plan_id = ec.plan_id
WHERE p.plan_uuid = '6bfca487-5e29-4eab-85c4-8626a5810142'
ORDER BY ec.employee_class_id;
*/
