[
  {
    "carriers": [
      "Current",
      "Manulife",
      "Victor",
      "Chambers Plan"
    ],
    "sections": [
      {
        "id": "lifeinsurance-manager",
        "name": "Life Insurance - manager",
        "benefits": [
          {
            "key": "coverageLife",
            "name": "Coverage LI",
            "values": {
              "Victor": "1 times annual earnings",
              "Current": "$100,000",
              "Manulife": "$25,000",
              "Canada Life": "-",
              "Chambers Plan": "1 times annual earnings"
            },
            "section": "lifeinsurance-manager"
          },
          {
            "key": "ageReduction",
            "name": "Age Reduction",
            "values": {
              "Victor": "50% at age 65",
              "Current": "50% at age 65",
              "Manulife": "50% at age 65",
              "Canada Life": "-",
              "Chambers Plan": "50% at age 65"
            },
            "section": "lifeinsurance-manager"
          },
          {
            "key": "terminationAgeLife",
            "name": "Termination Age - LI",
            "values": {
              "Victor": "Retirement or to age 70",
              "Current": "Retirement or to age 75",
              "Manulife": "Retirement or to age 75",
              "Canada Life": "-",
              "Chambers Plan": "Retirement or to age 75"
            },
            "section": "lifeinsurance-manager"
          }
        ]
      },
      {
        "id": "lifeinsurance-production",
        "name": "Life Insurance - production",
        "benefits": [
          {
            "key": "coverageLife",
            "name": "Amount of Coverage",
            "values": {
              "Victor": "1 times annual earnings",
              "Current": "$50,000",
              "Manulife": "$25,000",
              "Canada Life": "-",
              "Chambers Plan": "1 times annual earnings"
            },
            "section": "lifeinsurance-production"
          },
          {
            "key": "ageReduction",
            "name": "Age Reduction",
            "values": {
              "Victor": "50% at age 65",
              "Current": "50% at age 65",
              "Manulife": "50% at age 65",
              "Canada Life": "-",
              "Chambers Plan": "50% at age 65"
            },
            "section": "lifeinsurance-production"
          },
          {
            "key": "terminationAgeLife",
            "name": "Termination Age - LI",
            "values": {
              "Victor": "Retirement or to age 70",
              "Current": "Retirement or to age 75",
              "Manulife": "Retirement or to age 75",
              "Canada Life": "-",
              "Chambers Plan": "Retirement or to age 75"
            },
            "section": "lifeinsurance-production"
          }
        ]
      },
       {
        "id": "ADAD",
        "name": "Accidental Death & Dismembermenet - manager",
        "benefits": [
          {
            "key": "terminationAgeADAD",
            "name": "Termination Age - AD&D",
            "values": {
              "Victor": "Retirement or to age 70",
              "Current": "Retirement or to age 71",
              "Manulife": "Retirement or to age 71",
              "Canada Life": "-",
              "Chambers Plan": "Retirement or to age 75"
            },
            "section": "ADAD-manager"
          }
        ]
      },
      {
        "id": "ADAD-production",
        "name": "Accidental Death & Dismembermenet - production",
        "benefits": [
          {
            "key": "terminationAgeADAD",
            "name": "Termination Age - AD&D",
            "values": {
              "Victor": "Retirement or to age 70",
              "Current": "Retirement or to age 71",
              "Manulife": "Retirement or to age 71",
              "Canada Life": "-",
              "Chambers Plan": "Retirement or to age 75"
            },
            "section": "ADAD-production"
          }
        ]
      },   
      {
        "id": "dependentlife-manager",
        "name": "Dependent Life - manager",
        "benefits": [
          {
            "key": "spouse",
            "name": "Spouse",
            "values": {
              "Victor": "$10,000",
              "Current": "$10,000",
              "Manulife": "$5,000",
              "Canada Life": "-",
              "Chambers Plan": "$3,200"
            },
            "section": "dependentlife-manager"
          },
          {
            "key": "child",
            "name": "Child",
            "values": {
              "Victor": "$5,000",
              "Current": "$5,000",
              "Manulife": "$2,500",
              "Canada Life": "-",
              "Chambers Plan": "$3,200"
            },
            "section": "dependentlife-manager"
          }
        ]
      }
    ]
  },
  {
    "carriers": [
      "Current",
      "Manulife",
      "Victor",
      "Chambers Plan"
    ],
    "sections": [
      {
        "id": "dependentlife-production",
        "name": "Dependent Life - production",
        "benefits": [
          {
            "key": "spouse",
            "name": "Spouse",
            "values": {
              "Victor": "$10,000",
              "Current": "$10,000",
              "Manulife": "$5,000",
              "Canada Life": "-",
              "Chambers Plan": "$3,200"
            },
            "section": "dependentlife-production"
          },
          {
            "key": "child",
            "name": "Child",
            "values": {
              "Victor": "$5,000",
              "Current": "$5,000",
              "Manulife": "$2,500",
              "Canada Life": "-",
              "Chambers Plan": "$2,600"
            },
            "section": "dependentlife-production"
          }
        ]
      },
       {
        "id": "longtermdisability-manager",
        "name": "Long Term Disability - manager",
        "benefits": [
          {
            "key": "coverageLTD",
            "name": "Coverage",
            "values": {
              "Victor": "66.67%",
              "Current": "66.7%",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "67% first $3,500, 50% balance"
            },
            "section": "longtermdisability-manager"
          },
          {
            "key": "eliminationPeriod",
            "name": "Elimination Period",
            "values": {
              "Victor": "16 weeks",
              "Current": "119 days",
              "Manulife": "182 days",
              "Canada Life": "-",
              "Chambers Plan": "113 days"
            },
            "section": "longtermdisability-manager"
          },
            {
            "key": "benefitPeriod",
            "name": "Benefit Period",
            "values": {
              "Victor": "to age 65",
              "Current": "to age 65",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "to age 65"
            },
            "section": "longtermdisability-manager"
          },
          {
            "key": "benefitMaximumLTD",
            "name": "Benefit Maximum",
            "values": {
              "Victor": "$5,000",
              "Current": "$5,000",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "$8,000"
            },
            "section": "longtermdisability-manager"
          },
          {
            "key": "definitionOfDisability",
            "name": "Definition of Disability",
            "values": {
              "Victor": "2 year own occupation",
              "Current": "2 year own occupation",
              "Manulife": "any occupation",
              "Canada Life": "-",
              "Chambers Plan": "24 months own occupation"
            },
            "section": "longtermdisability-manager"
          },
          {
            "key": "taxable",
            "name": "Taxable",
            "values": {
              "Victor": "No",
              "Current": "-",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "No"
            },
            "section": "longtermdisability-manager"
          },
          {
            "key": "quoteToNEMOrMax",
            "name": "Quote to NEM or Max",
            "values": {
              "Victor": "-",
              "Current": "-",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "-"
            },
            "section": "longtermdisability-manager"
          },
          {
            "key": "terminationAgeLTD",
            "name": "Termination Age",
            "values": {
              "Victor": "Retirement or to age 65",
              "Current": "Retirement or to age 65",
              "Manulife": "Retirement or to age 65",
              "Canada Life": "-",
              "Chambers Plan": "Retirement or to age 65"
            },
            "section": "longtermdisability-manager"
          },
          {
            "key": "nonEvidenceMaximum",
            "name": "nonEvidenceMaximum",
            "values": {
              "Victor": "$4,500",
              "Current": "$3,500",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "$4,000"
            },
            "section": "longtermdisability-manager"
          }
        ]
      },
{
        "id": "longtermdisability-production",
        "name": "Long Term Disability - production",
        "benefits": [
          {
            "key": "coverageLTD",
            "name": "Coverage",
            "values": {
              "Victor": "66.67%",
              "Current": "66.7%",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "67% first $3,500, 50% balance"
            },
            "section": "longtermdisability-production"
          },
          {
            "key": "eliminationPeriod",
            "name": "Elimination Period",
            "values": {
              "Victor": "16 weeks",
              "Current": "119 days",
              "Manulife": "182 days",
              "Canada Life": "-",
              "Chambers Plan": "113 days"
            },
            "section": "longtermdisability-production"
          },
          {
            "key": "benefitPeriod",
            "name": "Benefit Period",
            "values": {
              "Victor": "to age 65",
              "Current": "to age 65",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "to age 65"
            },
            "section": "longtermdisability-production"
          },
           {
            "key": "benefitMaximumLTD",
            "name": "Benefit Maximum",
            "values": {
              "Victor": "$5,000",
              "Current": "$5,000",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "$8,000"
            },
            "section": "longtermdisability-production"
          },
          {
            "key": "definitionOfDisability",
            "name": "Definition of Disability",
            "values": {
              "Victor": "2 year own occupation",
              "Current": "2 year own occupation",
              "Manulife": "any occupation",
              "Canada Life": "-",
              "Chambers Plan": "24 months own occupation"
            },
            "section": "longtermdisability-production"
          },
          {
            "key": "taxable",
            "name": "Taxable",
            "values": {
              "Victor": "No",
              "Current": "-",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "No"
            },
            "section": "longtermdisability-production"
          },
          {
            "key": "quoteToNEMOrMax",
            "name": "Quote to NEM or Max",
            "values": {
              "Victor": "-",
              "Current": "-",
              "Manulife": "-",
              "Canada Life": "-",
              "Chambers Plan": "-"
            },
            "section": "longtermdisability-production"
          },
          {
            "key": "terminationAgeLTD",
            "name": "Termination Age",
            "values": {
              "Victor": "Retirement or to age 65",
              "Current": "Retirement or to age 65",
              "Manulife": "Retirement or to age 65",
              "Canada Life": "-",
              "Chambers Plan": "Retirement or to age 65"
            },
            "section": "longtermdisability-production"
          },
          {
            "key": "nonEvidenceMaximum",
            "name": "nonEvidenceMaximum",
            "values": {
              "Victor": "$4,500",
              "Current": "$3,500",
              "Manulife": "$25,000",
              "Canada Life": "-",
              "Chambers Plan": "$4,000"
            },
            "section": "longtermdisability-production"
          }
        ]
      },

     ......